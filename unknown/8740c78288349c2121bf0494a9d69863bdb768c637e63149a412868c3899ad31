import { But<PERSON> } from "@/components/ui/button";
import { useLocale, useTranslations } from "next-intl";
import Link from "next/link";
import {Locale} from "@/i18n/routing";

export default function NotFoundPage() {
  const t = useTranslations("common");
  const locale: Locale = useLocale() as Locale;
  return (
    <div className="flex flex-col items-center justify-center h-full bg-background text-foreground text-center container">
      <h1 className="text-4xl font-bold">{t("NotFoundPage")}</h1>
      <p className="text-muted-foreground mt-2">{t("PageNotFoundMessage")}</p>
      <Link href={`/${locale}`}>
        <Button className="mt-6">{t("GoHome")}</Button>
      </Link>
    </div>
  );
}
