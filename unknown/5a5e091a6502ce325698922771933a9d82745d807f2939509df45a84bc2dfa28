"use client";

import React from "react";
import { useTranslations } from "next-intl";
import { EmployeeProfileHeader } from "./employee-profile-header";
import { EmployeeProfileTabs } from "./employee-profile-tabs";
import { EmployeeProfileHeaderSkeleton } from "@/app/[locale]/_modules/people/skeletons/employee-profile-header-skeleton";
import {
  TEmployeeAttributes,
  TEmployeeData,
  TUserRoleIncluded,
} from "../../../type/employee";
import { TSalaryPackageData } from "../../../type/salary-package";

type EmployeeProfileLayoutProps = {
  employeeId: string;
  locale: string;
  employee: TEmployeeAttributes | null | undefined;
  employeeData: TEmployeeData | null | undefined;
  isLoading: boolean;
  error: string | null;
  userRoles?: TUserRoleIncluded[];
  children: React.ReactNode;
  salaryPackage?: TSalaryPackageData | null;
};

export const EmployeeProfileLayout: React.FC<EmployeeProfileLayoutProps> = ({
  employeeId,
  locale,
  employee,
  employeeData,
  isLoading,
  salaryPackage,
  error,
  userRoles = [],
  children,
}) => {
  const t = useTranslations();
  return (
    <div className="space-y-6 max-md:mt-6">
      {isLoading ? (
        <EmployeeProfileHeaderSkeleton />
      ) : error || !employee ? (
        <div className="flex flex-col gap-6 p-6 bg-white rounded-[20px] border border-gray-200 shadow-sm">
          <h2 className="text-xl font-semibold text-red-500">
            {t("common.Error.title")}
          </h2>
          <p className="text-gray-700">{error || "Employee not found"}</p>
        </div>
      ) : (
        <EmployeeProfileHeader
          employee={employee}
          employeeData={employeeData}
          salaryPackage={salaryPackage}
          userRoles={userRoles}
        />
      )}

      {/* Navigation Tabs */}
      <EmployeeProfileTabs employeeId={employeeId} locale={locale} />

      {/* Content Area  */}
      {children}
    </div>
  );
};
