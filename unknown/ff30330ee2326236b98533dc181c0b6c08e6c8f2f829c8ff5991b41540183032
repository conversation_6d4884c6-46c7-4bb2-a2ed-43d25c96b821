"use client";

import React, {
  startTransition,
  useActionState,
  useEffect,
  useRef,
} from "react";
import { useLocale, useTranslations } from "next-intl";
import { Button } from "@/components/ui/button";
import { Form } from "@/components/ui/form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { PAGES } from "@/enums";
import useFormFields from "@/hooks/useFormFields";
import { ActionState, TFunction, TinputField } from "@/types";
import FormFieldRenderer from "@/components/auth/fields/form-field-renderer";
import { LoaderCircle } from "lucide-react";
import { useToastMessage } from "@/hooks/use-toast-message";
import { format } from "date-fns";
import {
  SalaryPackageSchemaType,
  salaryPackageSchema,
} from "../../../schemas/salaryPackageSchema";
import { saveSalaryPackage } from "../../../actions/salary-package";
import {
  TSalaryPackageData,
  TSalaryPackageResponse,
} from "../../../type/salary-package";
import { formatNumber } from "@/lib/format-number";
import { Locale } from "@/i18n/routing";

// Salary Package Form Component
type SalaryPackageFormProps = {
  employeeId: string;
  onClose: () => void;
  existingPackage?: TSalaryPackageData | null;
  onSalaryPackageCreated?: (newPackage: TSalaryPackageData) => void;
};

const SalaryPackageForm = ({
  employeeId,
  onClose,
  existingPackage,
  onSalaryPackageCreated,
}: SalaryPackageFormProps) => {
  const t = useTranslations() as TFunction;
  const formRef = useRef<HTMLFormElement | null>(null);
  const { showToast } = useToastMessage();
  const { getFormFields } = useFormFields({
    formType: PAGES.CREATESALARYPACKAGE,
    t,
  });

  const initialState: ActionState<TSalaryPackageResponse> = {
    error: "",
    success: "",
    issues: [],
    data: null,
  };

  // Determine if we're in create or update mode
  const isUpdateMode = !!existingPackage;
  const formMode = isUpdateMode ? "update" : "create";
  const locale = useLocale() as Locale;
  const [state, submitAction, isPending] = useActionState(
    saveSalaryPackage,
    initialState,
  );

  // Get attributes from existing package if available
  const packageAttrs = existingPackage?.attributes || {
    base_salary: "",
    housing_allowance: "0",
    transportation_allowance: "0",
    other_allowances: "0",
    effective_date: "",
    end_date: null,
    status: "active" as const,
    adjustment_reason: null,
    previous_package_id: null,
    notes: "",
    created_at: "",
    updated_at: "",
    total_package_value: "",
  };

  // Parse effective date if it exists
  let effectiveDate = new Date();
  if (isUpdateMode && packageAttrs.effective_date) {
    try {
      effectiveDate = new Date(packageAttrs.effective_date);
    } catch (e) {
      console.error("Failed to parse effective date:", e);
    }
  }

  // Initialize form with default values
  const form = useForm<SalaryPackageSchemaType>({
    resolver: zodResolver(salaryPackageSchema(t, formMode)),
    defaultValues: {
      employee_id: employeeId,
      base_salary: isUpdateMode
        ? formatNumber(Number(packageAttrs.base_salary), locale)
        : "",
      housing_allowance: isUpdateMode
        ? formatNumber(Number(packageAttrs.housing_allowance), locale)
        : "0",
      transportation_allowance: isUpdateMode
        ? formatNumber(Number(packageAttrs.transportation_allowance), locale)
        : "0",
      other_allowances: isUpdateMode
        ? formatNumber(Number(packageAttrs.other_allowances), locale)
        : "0",
      effective_date: effectiveDate,
      notes: isUpdateMode ? packageAttrs.notes : "",
    },
    mode: "all",
  });

  useEffect(() => {
    if (state?.success) {
      showToast("success", state.success);

      if (state.data?.data && onSalaryPackageCreated) {
        onSalaryPackageCreated(state.data.data);
      }

      onClose();
    }

    if (state?.error) {
      showToast("error", state.issues?.[0] || state.error);
    }
  }, [state, onSalaryPackageCreated]);

  // Handle form submission
  const handleSubmit = (data: SalaryPackageSchemaType) => {
    const formData = new FormData();

    Object.entries(data).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        if (key === "effective_date" && value instanceof Date) {
          formData.append(key, format(value, "dd-MM-yyyy"));
        }
        // Handle numeric fields that might have commas
        else if (
          [
            "base_salary",
            "housing_allowance",
            "transportation_allowance",
            "other_allowances",
          ].includes(key) &&
          typeof value === "string"
        ) {
          // Remove commas before sending to backend
          const numericValue = value.replace(/,/g, "");
          formData.append(key, numericValue);
        }
        // Handle other fields
        else {
          formData.append(key, String(value));
        }
      }
    });

    startTransition(() => {
      submitAction(formData);
    });
  };

  return (
    <Form {...form}>
      <form
        ref={formRef}
        action={submitAction}
        onSubmit={form.handleSubmit(handleSubmit)}
        className="space-y-6"
      >
        {(
          (getFormFields() || []) as TinputField<SalaryPackageSchemaType>[]
        ).map((fieldConfig) => (
          <FormFieldRenderer
            key={fieldConfig.name}
            fieldConfig={fieldConfig}
            form={form}
            isPending={isPending}
          />
        ))}

        {/* Form buttons */}
        <div className="sticky border-t border-t-slate-200 pt-2.5 bottom-0 left-0 w-[100%] rounded-t-[18px] bg-white flex flex-row items-center justify-between gap-4 sm:gap-6">
          <Button
            disabled={isPending || !form.formState.isDirty}
            type="submit"
            className="w-full h-12 max-h-12"
          >
            {!isPending ? (
              t("common.buttonText.submit")
            ) : (
              <LoaderCircle className="animate-spin text-white" />
            )}
          </Button>
          <Button
            type="button"
            variant="outline"
            className="w-full h-12 max-h-12"
            disabled={isPending}
            onClick={() => onClose()}
          >
            {t("common.buttonText.cancel2")}
          </Button>
        </div>
      </form>
    </Form>
  );
};

export default SalaryPackageForm;
