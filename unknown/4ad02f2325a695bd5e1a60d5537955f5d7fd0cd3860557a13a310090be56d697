"use client";

import React, { useState, useEffect } from "react";
import { useTranslations } from "next-intl";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { BriefCase } from "../../../../../../../../public/images/icons";
import {
  TEmployee,
  TUserRoleIncluded,
  TEmployeeAttributes,
  TEmployeeData,
} from "../../../type/employee";
import dynamic from "next/dynamic";
import { DialogTitle } from "@/components/ui/dialog";
import LoaderPortal from "@/components/loader/loader-portal";
import SalaryPackageForm from "./salary-package-form";
import { TSalaryPackageData } from "../../../type/salary-package";
import { transformUserRolesToRoleProjectPairs } from "../../../utils/transform-user-roles";

const CalculatePeriodModal = dynamic(
  () =>
    import("./calculate-period-modal").then((mod) => mod.CalculatePeriodModal),
  {
    ssr: false,
    loading: () => <LoaderPortal overlayColor="#000" overlayOpacity={0.6} />,
  },
);

const ResponsiveDialog = dynamic(
  () => import("@/components/responsive-dialog"),
  {
    ssr: false,
    loading: () => <LoaderPortal overlayColor="#000" overlayOpacity={0.6} />,
  },
);

type EmployeeProfileHeaderProps = {
  employee: TEmployee | TEmployeeAttributes | null | undefined;
  employeeData?: TEmployeeData | null;
  userRoles?: TUserRoleIncluded[];
  salaryPackage?: TSalaryPackageData | null;
};

export const EmployeeProfileHeader: React.FC<EmployeeProfileHeaderProps> = ({
  employee,
  employeeData,
  salaryPackage: initialSalaryPackage,
  userRoles = [],
}) => {
  const t = useTranslations();
  const [showRolesModal, setShowRolesModal] = useState(false);
  const [showSalaryPackageModal, setShowSalaryPackageModal] = useState(false);
  const [showCalculatePeriodModal, setShowCalculatePeriodModal] =
    useState(false);
  const [salaryPackage, setSalaryPackage] = useState(initialSalaryPackage);

  // Update local state when prop changes
  useEffect(() => {
    setSalaryPackage(initialSalaryPackage);
  }, [initialSalaryPackage]);

  if (!employee) return null;

  const handleCreateSalaryPackage = () => {
    setShowSalaryPackageModal(true);
  };

  const handleCalculatePeriod = () => {
    setShowCalculatePeriodModal(true);
  };

  // Get role and project information from userRoles using the utility function
  const fullRoleProjectPairs = transformUserRolesToRoleProjectPairs(
    userRoles || [],
  );

  // Simplify the data for the header display (we don't need all fields)
  const roleProjectPairs = fullRoleProjectPairs.map(
    ({ roleId, projectId, roleName, projectName }) => ({
      roleId,
      projectId,
      roleName,
      projectName,
    }),
  );
  return (
    <>
      <div className="bg-white rounded-[20px] max-lg:px-3 p-6 border border-gray-200">
        <div className="flex flex-col lg:flex-row justify-between lg:gap-3 items-center">
          <div className="w-full flex flex-col lg:flex-row gap-6 justify-between lg:max-w-[900px] items-center">
            <div className="flex flex-col lg:flex-row gap-3 items-center">
              <div className="relative rounded-full">
                <Avatar className="object-cover w-[72px] h-[72px]">
                  <AvatarImage
                    className="object-cover object-top !w-20 !h-20"
                    src={
                      typeof employee.avatar_url === "string"
                        ? employee.avatar_url
                        : ""
                    }
                    alt={`${employee.name || ""} profile image`}
                  />
                  <AvatarFallback>
                    {(employee.name || "").charAt(0)}
                  </AvatarFallback>
                </Avatar>
              </div>

              <div className="flex flex-col gap-1 flex-1 xl:min-w-[120px]">
                <div className="text-center lg:text-start">
                  <h2 className="text-xl font-semibold leading-[30px] tracking-[0.5%]">
                    {employee.name}
                  </h2>
                </div>
              </div>
            </div>

            <span className="h-14 text-gray-400 bg-gray-100 w-0.5 hidden lg:block"></span>

            <div className="flex max-xl:flex-wrap gap-6 sm:gap-2 text-center sm:text-start justify-between sm:justify-around lg:justify-start lg:gap-6 items-center w-full text-sm">
              <div className="flex flex-col gap-1">
                <h3 className="text-gray-400">
                  {t("common.form.email.label")}
                </h3>
                <p className="font-medium max-w-48 break-words ">
                  {employee.email}
                </p>
              </div>
              <div className="flex flex-col gap-1 shrink-0">
                <h3 className="text-gray-400">
                  {t("common.form.mobile.label")}
                </h3>
                <p className="font-medium" dir="ltr">
                  {employee.phone || employee.phone_intl || "N/A"}
                </p>
              </div>
              <div className="flex flex-col gap-1">
                {roleProjectPairs.length > 0 && (
                  <div className="flex flex-col gap-1 text-start">
                    <h3 className="text-gray-400 text-sm font-medium">
                      {t(
                        "people.employees-page.profile.roles-projects.table.title",
                      )}
                    </h3>

                    <div className="flex items-center gap-1 rounded-lg p-0 ">
                      <span className="text-sm font-medium">
                        {roleProjectPairs[0].roleName} -{" "}
                        {roleProjectPairs[0].projectName}
                      </span>
                      {roleProjectPairs.length > 1 && (
                        <>
                          <span className="text-secondary">,</span>
                          <div
                            className="cursor-pointer hover:bg-gray-200 rounded-lg p-0.5 transition-colors border-none"
                            onClick={() => setShowRolesModal(true)}
                          >
                            <span className="flex rtl:flex-row-reverse items-center text-secondary underline">
                              <span>+</span>
                              {roleProjectPairs.length - 1}
                            </span>
                          </div>
                        </>
                      )}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Projects and Roles Section */}
          <div className="mt-6 lg:mt-0 w-full lg:w-auto flex flex-col items-center gap-3">
            <div className="w-full lg:w-auto max-sm:max-w-[295px]">
              <div className="w-full sm:w-11/12 mx-auto lg:w-auto flex flex-col gap-3">
                <Button
                  className={`w-full flex items-center gap-4 min-h-14 ${
                    !employee.email
                      ? "bg-gray-400 hover:bg-gray-400 cursor-not-allowed"
                      : "text-white"
                  }`}
                  onClick={handleCreateSalaryPackage}
                  disabled={!employee.email}
                  type="button"
                >
                  <span>
                    {!salaryPackage
                      ? t("people.salary-package.create.button")
                      : t("people.salary-package.update.button")}
                  </span>
                </Button>
                <Button
                  className={`w-full flex items-center gap-4 min-h-14 ${
                    !employee.email
                      ? "bg-gray-400 hover:bg-gray-400 cursor-not-allowed"
                      : "text-white"
                  }`}
                  onClick={handleCalculatePeriod}
                  disabled={!employee.email}
                  type="button"
                >
                  {t("people.salary-calculation.calculate-period.button")}
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Roles and Projects Modal */}
      {showRolesModal && (
        <ResponsiveDialog
          open={showRolesModal}
          onOpenChange={setShowRolesModal}
          header={
            <DialogTitle className="px-6 py-[22px] border-b font-semibold text-[18px] leading-[28px]">
              {t("people.employees-page.profile.roles-projects.table.title")}
            </DialogTitle>
          }
        >
          <div className="space-y-8">
            {roleProjectPairs.map((item, index) => (
              <div key={index} className="flex items-center gap-4 rounded-lg">
                <div className="rounded-lg bg-gray-100 w-10 h-10 flex items-center justify-center">
                  <BriefCase className="w-5 h-5 text-gray-700" />
                </div>
                <div className="flex flex-col">
                  <span className="font-semibold">{item.roleName}</span>
                  <span className="text-sm text-gray-500">
                    {item.projectName}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </ResponsiveDialog>
      )}

      {/* Salary Package Modal */}
      {showSalaryPackageModal && (
        <ResponsiveDialog
          open={showSalaryPackageModal}
          onOpenChange={setShowSalaryPackageModal}
          header={
            <DialogTitle className="px-6 py-[22px] border-b font-semibold text-[18px] leading-[28px]">
              {!salaryPackage
                ? t("people.salary-package.create.title")
                : t("people.salary-package.update.title")}
            </DialogTitle>
          }
          className="h-[93vh]"
        >
          <SalaryPackageForm
            employeeId={
              employeeData?.id !== undefined && employeeData?.id !== null
                ? String(employeeData.id)
                : ""
            }
            onClose={() => setShowSalaryPackageModal(false)}
            existingPackage={salaryPackage}
            onSalaryPackageCreated={(newPackage) => {
              setSalaryPackage(newPackage);
            }}
          />
        </ResponsiveDialog>
      )}

      {/* Calculate Period Modal */}
      {showCalculatePeriodModal && (
        <CalculatePeriodModal
          isOpen={showCalculatePeriodModal}
          onClose={() => setShowCalculatePeriodModal(false)}
          employeeId={employeeData?.id}
        />
      )}
    </>
  );
};
