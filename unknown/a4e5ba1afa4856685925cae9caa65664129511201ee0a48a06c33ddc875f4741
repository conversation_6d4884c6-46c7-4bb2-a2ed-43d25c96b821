"use client";

import React, { useEffect, useState } from "react";
import { <PERSON>, <PERSON> } from "../../../../../public/images/icons";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import EditProfileForm from "@/app/[locale]/_components/settings/edit-profile";
import ChangePasswordForm from "../../_components/settings/change-password";
import { ChevronLeft } from "lucide-react";
import NotificationSound from "../../_components/settings/notification-sound";
import { useTranslations } from "next-intl";

const MobSettingsContent = () => {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const t = useTranslations();

  // Initialize activeTab from URL, defaulting to an empty string (no selection)
  const initialTab = searchParams.get("tab") || "";
  const [activeTab, setActiveTab] = useState(initialTab);

  // Update the URL whenever activeTab changes using shallow routing.
  useEffect(() => {
    const params = new URLSearchParams(searchParams);
    if (activeTab) {
      params.set("tab", `${activeTab}`);
    } else {
      params.delete("tab");
    }
    router.replace(`${pathname}?${params.toString()}`);
  }, [activeTab, router, pathname, searchParams]);

  // If a tab is active, render the full-page form with a back button.
  if (activeTab) {
    return (
      <div className="flex flex-col min-h-[calc(100vh-225px)] relative max-md:pt-4 max-md:mt-0 md:mt-14">
        <Button
          variant={"ghost"}
          onClick={() => setActiveTab("")}
          className="absolute z-20 gap-0 flex items-center -top-8 md:-top-8 end-0 mb-4 text-secondary hover:bg-background-v2 p-2"
        >
          <span>{t("settings.buttons.back")}</span>
          <ChevronLeft className="!w-6 !h-6 text-[#1c1c1c] ltr:rotate-180" />
        </Button>
        {activeTab === "global" && (
          <EditProfileForm
            onPasswordChanged={() => setActiveTab("change-password")}
          />
        )}
        {activeTab === "notifications" && <NotificationSound />}
        {activeTab === "change-password" && (
          <ChangePasswordForm
            onPasswordChanged={() => setActiveTab("global")}
          />
        )}
      </div>
    );
  }

  // Otherwise, render the settings options.
  return (
    <div className="flex flex-col min-h-[calc(100vh-225px)] max-md:pt-4">
      <div className="flex flex-1 flex-col gap-2">
        {/* Global Settings Button */}
        <Button
          onClick={() => setActiveTab("global")}
          className={`flex justify-start items-center gap-3 text-[#1c1c1c] bg-neutral-100 hover:bg-background-v2 h-[45px] shadow-none`}
        >
          <Settings className="text-icons-main" />
          <span>{t("settings.global.title")}</span>
        </Button>
        {/* Notifications Settings Button */}
        <Button
          onClick={() => setActiveTab("notifications")}
          className={`flex justify-start items-center gap-3 text-[#1c1c1c] bg-neutral-100 hover:bg-background-v2 h-[45px] shadow-none`}
        >
          <Bell className="stroke-icons-main" />
          <span>{t("settings.notifications.title")}</span>
        </Button>
      </div>
      {/* Change Password Button */}
      <Button onClick={() => setActiveTab("change-password")} className="h-12">
        {t("common.settings.tabs.change-password")}
      </Button>
    </div>
  );
};

export default MobSettingsContent;
