"use client";

import { DataTable } from "@/components/table";
import React, { useState } from "react";
import { useLocale, useTranslations } from "next-intl";
import { PaginationWithLinks } from "@/components/pagination-with-links";
import { RowSelectionState } from "@tanstack/react-table";
import { employeeRolesProjectsColumns } from "./employee-roles-projects-columns";
import { useSearchParams } from "next/navigation";
import { useEmployeeRoleProjectMutations } from "../../../../hooks/employees/useEmployeeRoleProjectMutations";
import { TRoleProjectPair } from "../../../../type/employee-roles-projects";

type EmployeeRolesProjectsTableProps = {
  employeeId: string;
  roleProjectPairs: TRoleProjectPair[];
  isLoading: boolean;
  error: string | null;
  mutateData: () => void;
};

export const EmployeeRolesProjectsTable = ({
  employeeId,
  roleProjectPairs,
  isLoading,
  error,
  mutateData,
}: EmployeeRolesProjectsTableProps) => {
  const t = useTranslations();
  const locale = useLocale();
  const searchParams = useSearchParams();
  const limit = searchParams.get("limit") ?? "5";
  const page = searchParams.get("page") ?? "1";
  const [rowSelection, setRowSelection] = useState<RowSelectionState>({});

  // Get mutations for employee roles and projects
  const { deleteRoleProject, isDeleting } = useEmployeeRoleProjectMutations({
    employeeId,
    onSuccess: () => {
      mutateData();
    },
  });

  // Calculate pagination
  const totalCount = roleProjectPairs.length;
  const pageSize = Number(limit);
  const currentPage = Number(page);
  const startIndex = (currentPage - 1) * pageSize;
  const endIndex = Math.min(startIndex + pageSize, totalCount);

  // Slice the data for the current page
  const paginatedData = roleProjectPairs.slice(startIndex, endIndex);

  return (
    <div className="mt-6">
      <DataTable
        data={paginatedData}
        dataCount={totalCount}
        columns={employeeRolesProjectsColumns}
        tableContainerClass="min-h-[240px]"
        title={t("people.employees-page.profile.tabs.roles-projects")}
        meta={{
          t,
          locale: locale,
          onDeleteRoleProject: deleteRoleProject,
          isDeleting,
        }}
        rowSelection={rowSelection}
        onRowSelectionChange={setRowSelection}
        translationPrefix="people.employees-page.profile.roles-projects.table"
        isLoading={isLoading}
        initialLimit={5}
        hideFilters={true}
        hideSearch={true}
        error={error ? new Error(error) : null}
      />

      {/* Pagination */}
      <div className="w-full pt-[18px]">
        <PaginationWithLinks
          page={Number(page)}
          pageSize={Number(limit)}
          totalCount={totalCount}
          firstLastCounts={{
            firstCount: startIndex + 1,
            lastCount: endIndex,
          }}
          isLoading={isLoading}
          pageSizeSelectOptions={{
            pageSizeOptions: [5, 10, 25, 30, 45, 50],
            pageSizeSearchParam: "limit",
          }}
        />
      </div>
    </div>
  );
};
