@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --background-v2: 104, 33%, 94%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 99, 80%, 78%;
    --primary-foreground: 210 40% 98%;
    --secondary: 170, 41%, 20%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --secondary-2: 170, 41%, 20%;
    --sedondary-dark: 168, 12%, 16%; /*#242E2C*/
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 170, 2%, 90%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --success: 156, 88%, 16%;
    --hang: 47, 100%, 43%;
    --wait: 214, 69%, 55%;
    --error: 0, 63%, 31%;
    --error-600: 0, 72%, 51%;
    /* icons */
    --main-icon: 0, 0%, 70%;
    /* auth */
    --auth-primary: 218, 10%, 16%;
    --auth-secondary: 219, 14%, 40%, 1;
    --radius: 0.5rem;
    --disabled-text: 225, 14.6%, 83.9%;
    /* calender colors  */
    --fc-border-color: #e9eaea;

    --fc-small-font-size: 1px;
    --fc-page-bg-color: #fff;
    --fc-neutral-bg-color: rgba(15, 11, 11, 0.3);
    --fc-neutral-text-color: #808080;
    --fc-border-color: #ddd;

    --fc-button-text-color: hsla(224, 12%, 51%, 1);
    --fc-button-bg-color: white;
    --fc-button-border-color: hsl(180deg 2.33% 91.57%);
    --fc-button-hover-bg-color: hsl(var(--secondary));
    --fc-button-hover-border-color: hsl(var(--secondary));
    --fc-button-active-bg-color: hsl(var(--secondary));
    --fc-button-active-border-color: hsl(var(--secondary));

    --fc-event-bg-color: hsla(104, 33%, 94%, 1);
    --fc-event-border-color: hsla(104, 33%, 94%, 1);
    --fc-event-text-color: hsl(var(--secondary));
    --fc-event-selected-overlay-color: rgba(0, 0, 0, 0.25);

    --fc-more-link-bg-color: #d0d0d0;
    --fc-more-link-text-color: inherit;

    --fc-event-resizer-thickness: 8px;
    --fc-event-resizer-dot-total-width: 8px;
    --fc-event-resizer-dot-border-width: 1px;

    --fc-non-business-color: rgba(215, 215, 215, 0.3);
    --fc-bg-event-color: hsl(var(--secondary));
    --fc-bg-event-opacity: 0.3;
    --fc-highlight-color: rgba(188, 232, 241, 0.3);
    --fc-today-bg-color: hsl(var(--secondary), 0.1);
    --fc-now-indicator-color: red;
  }
  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  html {
    @apply custom-scroll;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@layer components {
  .container {
    @apply max-w-full mx-auto px-4 md:px-7;
  }
}

.success-toast {
  @apply bg-background-v2 shadow-sm shadow-white text-secondary border border-secondary text-start;
}
.error-toast {
  @apply bg-error text-white text-start;
}

body,
html,
:root {
  height: 100%;
}

/*---break---*/

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

.custom-scroll {
  overflow-x: auto;
  scrollbar-width: thin; /* For Firefox */
  scrollbar-color: hsl(var(--secondary)) hsl(var(--background-v2));
  z-index: 999999;
}

/* WebKit (Chrome, Safari, etc.) */
.custom-scroll::-webkit-scrollbar {
  width: 1px !important;
  height: 1px !important;
  z-index: 999999;
}

.custom-scroll::-webkit-scrollbar-track {
  background: hsl(var(--background-v2));
  border-radius: 20px;
  z-index: 999999;
}

.custom-scroll::-webkit-scrollbar-thumb {
  background-color: hsl(var(--secondary));
  border: 1px solid #e0e0e0;
  border-radius: 1px;
  z-index: 999999;
}

/* table header */
/* toolbar */
.fc-toolbar {
  display: flex;
  align-items: center;
  flex-direction: row-reverse;
}

.fc-toolbar-title {
  font-size: 16px !important;
  font-weight: 600 !important;
  text-align: center;
  flex-grow: 1;
}

.fc-toolbar-chunk:first-child {
  height: 0;
  z-index: 1;
}
.fc-toolbar-chunk {
  display: flex;
  align-items: center;
}

button.fc-placeholder-button.fc-button.fc-button-primary {
  all: unset;
  width: 81px;
  height: 0px;
}

/* header */
.fc .fc-header-toolbar.fc-toolbar {
  border: 1px solid;
  border-bottom: 0;
  padding: 16px 24px;
  border-radius: 24px 24px 0px 0px;
  border-color: hsl(var(--border));
  margin-bottom: 0;
}

a.fc-col-header-cell-cushion {
  color: hsla(224, 12%, 51%, 1);
  font-weight: 500;
  font-size: 12px;
}

button.fc-prev-button.fc-button.fc-button-primary {
  margin-inline-end: 12px;
}
button.fc-next-button.fc-button.fc-button-primary {
  margin-inline-start: 24px;
}
button.fc-prev-button.fc-button.fc-button-primary,
button.fc-next-button.fc-button.fc-button-primary {
  background: none;
  border: 1px solid #e9eaea;
  font-size: 12px;
  font-weight: bold;
  cursor: pointer;
  border-radius: 10px;
  width: 32px;
  height: 32px;
  padding: 0;
}
button.fc-prev-button.fc-button.fc-button-primary:active,
button.fc-next-button.fc-button.fc-button-primary:active {
  color: white;
}
/* end header */

.fc-daygrid-day-frame.fc-scrollgrid-sync-inner {
  padding-top: 16px;
}
thead[role="presentation"] tr {
  height: 50px;
  line-height: 50px;
}

.fc-daygrid-day-top {
  justify-content: center;
}

.fc-daygrid-event-harness {
  max-height: 129px;
  display: flex;
  justify-content: center;
}

.fc .fc-button .fc-icon {
  color: "hsla(224, 12%, 51%, 1)";
}

/***************** table body ******************/

button.fc-today-button.fc-button.fc-button-primary {
  background: hsl(var(--secondary), 1);
  color: white;
  border-radius: 12px;
  min-width: 81px;
  min-height: 40px;
}

button.fc-today-button.fc-button.fc-button-primary:active {
  color: white;
}
button.fc-today-button.fc-button.fc-button-primary:disabled {
  color: inherit;
  background: none;
}
.fc {
  min-height: 85vh;
  width: 100%;
  min-width: 375px;
}

.fc-customDropdown-button {
  background: none;
  font-size: 16px;
  color: #333;
  cursor: pointer;
}

.fc-h-event .fc-event-main {
  text-align: right;
  display: flex;
  justify-content: center;
  align-items: center;
}
.fc-direction-ltr .fc-daygrid-event.fc-event-end,
.fc-direction-rtl .fc-daygrid-event.fc-event-start {
  padding: 0;
  margin-bottom: 8px;
}
.event-ppacing {
  display: flex;
  flex-direction: column;
  align-items: start;
  justify-content: center;
  text-align: start;
  font-size: 12px;
  font-weight: 600;
  border-radius: 8px;
  width: 100%;
  max-width: 100%;
  padding: 4px 8px;
  height: 100%;
  line-height: 18px;
  text-align: start;
  word-break: break-word;
  word-break: break-word;
  display: inline-block;
  overflow-wrap: break-word;
  white-space: normal;
  letter-spacing: 0.5%;
}

.fc .fc-daygrid-more-link {
  font-size: 12px;
  background: hsla(220, 12%, 95%, 1);
  color: hsla(224, 12%, 51%, 1);
  border-radius: 8px;
  text-align: start;
  line-height: 18px;
  width: 100%;
  padding: 4px 8px;
}
.fc-theme-standard .fc-popover-header {
  background: hsl(var(--secondary));
  color: #fff;
}

.fc .fc-daygrid-day.fc-day-today {
  background: hsl(var(--background-v2), 0.2);
}
/*************** end table body **************/

@media (max-width: 768px) {
  .fc-toolbar-chunk:first-child {
    display: none;
  }
  button.fc-placeholder-button.fc-button.fc-button-primary {
    display: none;
  }
  .fc .fc-daygrid-more-link {
    padding: 4px 1px;
    font-weight: 600;
    text-align: center;
    font-size: 12px;
  }
  .event-spacing {
    width: 100%;
    padding: 2px;
    font-size: 10px;
    padding: 4px 1px;
  }
}
