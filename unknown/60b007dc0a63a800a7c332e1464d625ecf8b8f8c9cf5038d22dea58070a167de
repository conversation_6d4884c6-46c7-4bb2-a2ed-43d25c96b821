"use client";

import React, { useState } from "react";
import { useEmployeeAttachments } from "../../../../hooks/employees/useEmployeeAttachments";
import AttachmentCard from "./attachment-card";
import AttachmentSkeleton from "./attachment-skeleton";
import RenameAttachmentDialog from "./rename-attachment-dialog";
import DeleteAttachmentDialog from "./delete-attachment-dialog";
import { TEmployeeAttachment } from "../../../../type/employee";
import { useTranslations } from "next-intl";

type EmployeeAttachmentsProps = {
  employeeId: string;
};

const EmployeeAttachments = ({ employeeId }: EmployeeAttachmentsProps) => {
  const t = useTranslations();
  const {
    attachments,
    isLoading,
    error,
    renameAttachment,
    deleteAttachment,
    isRenaming,
    isDeleting,
  } = useEmployeeAttachments(employeeId);

  // State for dialogs
  const [selectedAttachment, setSelectedAttachment] =
    useState<TEmployeeAttachment | null>(null);
  const [isRenameDialogOpen, setIsRenameDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);

  // Handle rename click
  const handleRenameClick = (attachment: TEmployeeAttachment) => {
    setSelectedAttachment(attachment);
    setIsRenameDialogOpen(true);
  };

  // Handle delete click
  const handleDeleteClick = (attachment: TEmployeeAttachment) => {
    setSelectedAttachment(attachment);
    setIsDeleteDialogOpen(true);
  };

  // Common styles
  const containerClass = "mt-0 min-h-[220px]";
  const gridClass =
    "grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4";

  // Render loading state
  if (isLoading) {
    return (
      <div className={containerClass}>
        <div className={gridClass}>
          {Array.from({ length: 8 }).map((_, index) => (
            <AttachmentSkeleton key={index} />
          ))}
        </div>
      </div>
    );
  }

  // Render error state
  if (error) {
    return (
      <div className={containerClass}>
        <div className="bg-red-50 p-4 rounded-lg text-red-600 min-h-[220px] flex items-center justify-center">
          {t("people.employees-page.profile.attachments.error")}
        </div>
      </div>
    );
  }

  // Render empty state
  if (attachments.length === 0) {
    return (
      <div className={containerClass}>
        <div className="text-center py-12 min-h-[220px] flex flex-col items-center justify-center">
          <h3 className="text-xl font-medium mb-2">
            {t("people.employees-page.profile.attachments.empty.title")}
          </h3>
          <p className="text-gray-500">
            {t("people.employees-page.profile.attachments.empty.description")}
          </p>
        </div>
      </div>
    );
  }

  // Render attachments
  return (
    <div className={containerClass}>
      <div className={gridClass}>
        {attachments.map((attachment) => (
          <AttachmentCard
            key={attachment.id}
            attachment={attachment}
            onRenameClick={handleRenameClick}
            onDeleteClick={handleDeleteClick}
          />
        ))}
      </div>

      {/* Rename Dialog */}
      <RenameAttachmentDialog
        attachment={selectedAttachment}
        isOpen={isRenameDialogOpen}
        onClose={() => setIsRenameDialogOpen(false)}
        onRename={renameAttachment}
        isRenaming={isRenaming}
      />

      {/* Delete Dialog */}
      <DeleteAttachmentDialog
        attachment={selectedAttachment}
        isOpen={isDeleteDialogOpen}
        onClose={() => setIsDeleteDialogOpen(false)}
        onDelete={deleteAttachment}
        isDeleting={isDeleting}
      />
    </div>
  );
};

export default EmployeeAttachments;
