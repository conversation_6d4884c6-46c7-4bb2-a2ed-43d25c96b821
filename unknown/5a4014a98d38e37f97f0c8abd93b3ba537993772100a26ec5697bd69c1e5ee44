"use client";

import React, { useEffect, useState } from "react";
import { useTranslations } from "next-intl";
import { useStatistic } from "../../../hooks/employees/useEmployeeStatistics";
import { MetricCardsSkeleton } from "../../../skeletons/metric-card-skeleton";
import { MetricCard } from "../../common/metric-card";
import { MetricCardTypes } from "@/enums/statistics";

type EmployeeLeavesCardsProps = {
  employeeId: string;
};

export const EmployeeLeavesCards: React.FC<EmployeeLeavesCardsProps> = ({
  employeeId,
}) => {
  const t = useTranslations();

  // Ensure client-only rendering to avoid hydration mismatch
  const [mounted, setMounted] = useState(false);
  useEffect(() => {
    setMounted(true);
  }, []);

  const { metricCard, isLoading, error } = useStatistic(employeeId, [
    MetricCardTypes.PendingRequests,
    MetricCardTypes.WorkHours,
    MetricCardTypes.AverageDailyWorkHours,
  ]);

  if (!mounted) {
    return <MetricCardsSkeleton count={4} />;
  }

  if (error) {
    return (
      <div className="text-error p-4 min-h-[148px] border rounded-[20px] flex justify-center items-center">
        {error.message || t("common.errorLoadingAttendanceData")}
      </div>
    );
  }

  if (isLoading) {
    return <MetricCardsSkeleton count={4} />;
  }

  if (!metricCard || metricCard.length === 0) {
    return (
      <div className="p-4 min-h-[148px] border rounded-[20px] flex justify-center items-center text-muted">
        {t("common.noStatisticsAvailable")}
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-5">
      {metricCard.map((metric) => (
        <MetricCard
          key={metric.id}
          title={metric.attributes.title}
          value={`${metric.attributes.value}`}
          percentageChange={metric.attributes.comparison.percentage}
          comparisonText={metric.attributes.comparison.text}
        />
      ))}
    </div>
  );
};
