"use client";
import { DataTable } from "@/components/table";
import { useState } from "react";
import { columns } from "./attendance-requests-columns";
import { useLocale, useTranslations } from "next-intl";
import { PaginationWithLinks } from "@/components/pagination-with-links";
import { RowSelectionState } from "@tanstack/react-table";
import { useSearchParams } from "next/navigation";
import { useAttendanceListMutation } from "../../../hooks/useAttendanceListMutation";

const AttendanceRequestsTable = ({
  showPagination = false,
  searchParams: serverSearchParams,
}: {
  showPagination?: boolean;
  searchParams?: { page: string; limit: string };
}) => {
  const t = useTranslations();
  const locale = useLocale();
  const clientSearchParams = useSearchParams();

  // Use server-provided params if available, otherwise fall back to client-side params
  const limit =
    serverSearchParams?.limit ?? clientSearchParams.get("limit") ?? "5";
  const page =
    serverSearchParams?.page ?? clientSearchParams.get("page") ?? "1";
  const [rowSelection, setRowSelection] = useState<RowSelectionState>({});

  const searchQuery = clientSearchParams.get("search") || "";
  const filters = clientSearchParams.getAll("filters") || "";
  const filterQuery = [
    filters ? decodeURIComponent(filters.toString()) : null,
    searchQuery ? `filter[search]=${decodeURIComponent(searchQuery)}` : null,
  ]
    .filter(Boolean)
    .join("&");
  const { meta, attendanceList, employeeData, isLoading, error } =
    useAttendanceListMutation(
      Number(page),
      Number(limit),
      filterQuery ? filterQuery : "",
    );

  const totalCount = meta?.pagination?.count || 0;

  const firstResult = meta?.pagination.from || 1;
  const lastResult = meta?.pagination.to || 1;

  return (
    <>
      <DataTable
        data={attendanceList || []}
        columns={columns}
        tableContainerClass="min-h-[240px]"
        title={t(`people.attendance-requests-page.table.title`)}
        meta={{
          t,
          locale,
          employeeData: employeeData,
        }}
        rowSelection={rowSelection}
        onRowSelectionChange={setRowSelection}
        translationPrefix="people.attendance-requests-page.table"
        isLoading={isLoading}
        initialLimit={5}
        error={error}
        dataCount={totalCount}
      />
      {showPagination && (
        <div className="w-full pt-[18px]">
          <PaginationWithLinks
            page={Number(page)}
            pageSize={Number(limit)}
            totalCount={Number(totalCount)}
            firstLastCounts={{
              firstCount: firstResult ?? 1,
              lastCount: lastResult ?? 3,
            }}
            isLoading={isLoading}
            isDisabled={!attendanceList?.length}
            pageSizeSelectOptions={{
              pageSizeOptions: [5, 10, 25, 30, 45, 50],
              pageSizeSearchParam: "limit",
            }}
          />
        </div>
      )}
    </>
  );
};

export default AttendanceRequestsTable;
