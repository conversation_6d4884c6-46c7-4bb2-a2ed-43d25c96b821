"use client";

import React from "react";
import { EmployeeRolesProjectsTable } from "./employee-roles-projects-table";
import { useEmployeeDetails } from "../../../../hooks/employees/useEmployeeDetails";
import { transformUserRolesToRoleProjectPairs } from "../../../../utils/transform-user-roles";

type EmployeeRolesProjectsProps = {
  employeeId: string;
};

const EmployeeRolesProjects = ({ employeeId }: EmployeeRolesProjectsProps) => {
  const {
    isLoading,
    error,
    userRoles,
    mutate: mutateData,
  } = useEmployeeDetails(employeeId);

  const roleProjectPairs = transformUserRolesToRoleProjectPairs(
    userRoles || [],
  );
  // Common styles
  const containerClass = "mt-0 min-h-[220px]";

  // Render roles and projects table
  return (
    <div className={containerClass}>
      <EmployeeRolesProjectsTable
        employeeId={employeeId}
        roleProjectPairs={roleProjectPairs}
        isLoading={isLoading}
        error={error}
        mutateData={mutateData}
      />
    </div>
  );
};

export default EmployeeRolesProjects;
