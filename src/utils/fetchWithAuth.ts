"use client";

import { handleTokenExpiration, isTokenExpirationError } from './auth';

/**
 * Fetch wrapper that handles token expiration
 * @param url The URL to fetch
 * @param options Fetch options
 * @param locale The current locale
 */
export const fetchWithAuth = async <T>(
  url: string, 
  options: RequestInit = {}, 
  locale: string = 'en'
): Promise<T> => {
  try {
    // Ensure headers and credentials are set
    const fetchOptions: RequestInit = {
      ...options,
      headers: {
        "Content-Type": "application/json",
        ...options.headers,
      },
      credentials: "include", // This ensures cookies are sent with the request
    };

    const response = await fetch(url, fetchOptions);

    // If response is not ok, check for token expiration
    if (!response.ok) {
      // Try to parse the response as JSON
      try {
        const data = await response.json();
        
        // Check if the error is related to token expiration
        if (data && data.error && isTokenExpirationError(data.error)) {
          console.error('Token expiration detected in API response:', data.error);
          handleTokenExpiration(locale);
          throw new Error('Token expired. Redirecting to login...');
        }
        
        // For other errors, throw with the error message
        throw new Error(data.error || `API error: ${response.statusText}`);
      } catch (parseError) {
        // If we can't parse the response, just check the status code
        if (response.status === 401) {
          console.error('Unauthorized response (401)');
          handleTokenExpiration(locale);
          throw new Error('Unauthorized. Redirecting to login...');
        }
        
        // For other errors, throw with status text
        throw new Error(`API error: ${response.statusText}`);
      }
    }

    // If response is ok, return the parsed JSON
    return await response.json() as T;
  } catch (error) {
    // Check if it's a token expiration error that wasn't caught above
    if (isTokenExpirationError(error)) {
      console.error('Token expiration detected in catch block:', error);
      handleTokenExpiration(locale);
      throw new Error('Token expired. Redirecting to login...');
    }
    
    // Rethrow other errors
    console.error('Error in fetchWithAuth:', error);
    throw error;
  }
};
