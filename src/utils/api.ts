import { TSystems } from "@/types";

export function buildApiUrl(subsystem: TSystems, path?: string): string {
  const baseUrlPattern = process.env.BASE_API_URL;
  if (!baseUrlPattern) {
    throw new Error(
      "BASE_API_URL is not defined in the environment variables.",
    );
  }

  // Replace the {subsystem} placeholder in the base URL
  const baseUrl = baseUrlPattern.replace("{subsystem}", subsystem);

  // If path is provided, ensure it starts with a slash and append it to the base URL
  if (path) {
    const formattedPath = path.startsWith("/") ? path : `/${path}`;
    return `${baseUrl}${formattedPath}`;
  }

  // Return the base URL if no path is provided
  return baseUrl;
}
