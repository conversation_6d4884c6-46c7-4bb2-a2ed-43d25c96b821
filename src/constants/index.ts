import { SidebarData, TSystem, TSystems, TFunction } from "@/types";
import {
  HeartHealth,
  Calendar,
  Health,
  HealthHome,
  Report,
  SquaresFour,
  Appointment,
  Patient,
  Video,
} from "../../public/images/health/icons";
import {
  <PERSON>,
  Profile,
  Profile2User,
  Element1,
  Box,
  DoubleNote,
  DollarSquare,
} from "../../public/images/icons";

import {
  TEmployeesSalaries,
  TEmployeesSalariesResponse,
} from "@/app/[locale]/_modules/people/type/employees-salaries";
import { SYSTEM } from "./enum";
import { PermissionEnum } from "@/enums/Permission";

export const getSystemsArr = (t: TFunction): TSystem[] => {
  return [
    {
      href: `/${SYSTEM.PROCURE}`,
      name: `${SYSTEM.PROCURE}`,
      display_Name: t(`auth.selectSystem.procure.title`),
      title: "AtharProcure",
      description: t(`auth.selectSystem.procure.description`),
      img: "/images/systems-icons/AtharProcure.svg",
      alt: t(`auth.selectSystem.procure.alt`),
    },
    {
      href: `/${SYSTEM.PEOPLE}`,
      name: `${SYSTEM.PEOPLE}`,
      display_Name: t(`auth.selectSystem.people.title`),
      title: "AtharPeople",
      description: t(`auth.selectSystem.people.description`),
      img: "/images/systems-icons/AtharPeople.svg",
      alt: t(`auth.selectSystem.people.alt`),
    },
    {
      href: `/${SYSTEM.CM}`,
      name: `${SYSTEM.CM}`,
      display_Name: t(`auth.selectSystem.cm.title`),
      title: "AtharCM",
      description: t(`auth.selectSystem.cm.description`),
      img: "/images/systems-icons/AtharCM.svg",
      alt: t(`auth.selectSystem.cm.alt`),
    },
  ];
};

export const getSidebarData = (
  t: TFunction,
): Partial<Record<TSystems, SidebarData>> => {
  return {
    people: {
      head: "AtharHR",
      icon: "Profile2User",
      links: ["الرئيسية", "الطلبات", "الرواتب"],
      items: [
        {
          title: t(`common.sidebar.links.people`),
          url: `/${SYSTEM.PEOPLE}`,
          icon: "SquaresFour",
          permission: [],
        },
        {
          title: t("common.sidebar.links.employees"),
          url: `/${SYSTEM.PEOPLE}/employees`,
          icon: "Profile2User",
          permission: [
            PermissionEnum.MANAGE_EMPLOYEE,
            PermissionEnum.READ_EMPLOYEE,
            PermissionEnum.CREATE_EMPLOYEE,
            PermissionEnum.UPDATE_EMPLOYEE,
          ],
        },
        {
          title: t("common.sidebar.links.requests"),
          url: `/${SYSTEM.PEOPLE}/requests`,
          icon: "DoubleNote",
          permission: [PermissionEnum.MANAGE_LEAVE, PermissionEnum.READ_LEAVE],
        },
        {
          title: t("common.sidebar.links.attendance"),
          url: `/${SYSTEM.PEOPLE}/attendance`,
          icon: "DoubleNote",
          permission: [],
        },
        {
          title: t("common.sidebar.links.salaries"),
          url: `/${SYSTEM.PEOPLE}/salaries`,
          icon: "DollarSquare",
          permission: [
            PermissionEnum.READ_SALARY_PACKAGE,
            PermissionEnum.MANAGE_SALARY_PACKAGE,
          ],
        },
      ],
    },
    procure: {
      head: "AtherProcure",
      items: [
        {
          title: t(`common.sidebar.links.procure`),
          url: `/${SYSTEM.PROCURE}`,
          icon: "Element1",
          permission: [],
        },
        {
          title: t("common.sidebar.links.requests"),
          url: `/${SYSTEM.PROCURE}/requests`,
          icon: "Box",
          permission: [],
        },
        {
          title: t("common.sidebar.links.products"),
          url: `/${SYSTEM.PROCURE}/products`,
          icon: "Box",
          permission: [],
        },
      ],
    },
    cm: {
      head: "AtharCM",
      items: [
        {
          title: t(`common.sidebar.links.cm`),
          url: `/${SYSTEM.CM}`,
          icon: "SquaresFour",
          permission: [],
        },
        {
          title: t("common.sidebar.links.patients"),
          url: `/${SYSTEM.CM}/patients`,
          icon: "Calendar",
          permission: [],
        },
        {
          title: t("common.sidebar.links.appointments"),
          url: `/${SYSTEM.CM}/appointments`,
          icon: "Report",
          permission: [],
        },
      ],
    },
  };
};

export const DAYS = [
  { key: "monday", value: "monday" },
  { key: "tuesday", value: "tuesday" },
  { key: "wednesday", value: "wednesday" },
  { key: "thursday", value: "thursday" },
  { key: "friday", value: "friday" },
  { key: "saturday", value: "saturday" },
  { key: "sunday", value: "sunday" },
];

export const iconMap: Record<
  string,
  React.FC<React.SVGProps<SVGSVGElement>>
> = {
  HeartHealth,
  Calendar,
  Health,
  HealthHome,
  Report,
  SquaresFour,
  Bell,
  Profile,
  Appointment,
  Video,
  Patient,
  Element1,
  Box,
  Profile2User,
  DoubleNote,
  DollarSquare,
};

export const fetchSalariesData =
  async (): Promise<TEmployeesSalariesResponse> => {
    const salariesData: TEmployeesSalaries[] = [
      {
        id: "1",
        employeeName: "آية محمد",
        month: "فبراير",
        totalHours: 240,
        status: "مدفوع",
        paymentDate: "2024-02-28",
        numberOfLeaves: 2,
        overtimeHours: 20,
        hourlyRate: 25,
        totalDiscount: 300,
        tax: 0,
        totalSalary: 4500,
        notes: "Worked on a special project with excellent performance.",
      },
      {
        id: "2",
        employeeName: "أحمد علي",
        month: "مارس",
        totalHours: 233,
        status: "مدفوع",
        paymentDate: "2024-03-30",
        numberOfLeaves: 1,
        overtimeHours: 18,
        hourlyRate: 25,
        totalDiscount: 250,
        tax: 0,
        totalSalary: 4400,
        notes: "Met targets with steady performance.",
      },
      {
        id: "3",
        employeeName: "سارة خالد",
        month: "إبريل",
        totalHours: 220,
        status: "قيد الانتظار",
        paymentDate: null,
        numberOfLeaves: 3,
        overtimeHours: 15,
        hourlyRate: 22,
        totalDiscount: 400,
        tax: 0,
        totalSalary: 4000,
        notes: "Pending salary approval due to budget review.",
      },
      {
        id: "4",
        employeeName: "محمود حسن",
        month: "مايو",
        totalHours: 245,
        status: "مرفوض",
        paymentDate: null,
        numberOfLeaves: 0,
        overtimeHours: 25,
        hourlyRate: 30,
        totalDiscount: 0,
        tax: 0,
        totalSalary: 5500,
        notes: "Salary suspended due to policy violation investigation.",
      },
      {
        id: "5",
        employeeName: "ليلى عماد",
        month: "يونيو",
        totalHours: 230,
        status: "مدفوع",
        paymentDate: "2024-06-25",
        numberOfLeaves: 2,
        overtimeHours: 17,
        hourlyRate: 26,
        totalDiscount: 350,
        tax: 0,
        totalSalary: 4300,
        notes: "Outstanding performance in team projects.",
      },
      {
        id: "6",
        employeeName: "يوسف كريم",
        month: "يوليو",
        totalHours: 228,
        status: "قيد الانتظار",
        paymentDate: null,
        numberOfLeaves: 4,
        overtimeHours: 12,
        hourlyRate: 24,
        totalDiscount: 500,
        tax: 0,
        totalSalary: 4200,
        notes: "Pending final review before payment.",
      },
    ];

    return {
      salariesData,
      totalCount: salariesData.length,
    };
  };

export const fetchSalariesCalculations = async (
  pageNumber: number = 1,
  pageSize: number = 3,
): Promise<TEmployeesSalariesResponse> => {
  const response = await fetch(
    `/api/finance/salary_calculations?page[number]=${pageNumber}&page[size]=${pageSize}`,
  );
  if (!response.ok) {
    throw new Error("Failed to fetch salaries data");
  }
  const data = await response.json();

  // You may need to transform data to match TEmployeesSalaries[] if API shape differs
  const salariesData: TEmployeesSalaries[] = data?.data || [];

  return {
    salariesData,
    totalCount: salariesData.length,
  };
};
