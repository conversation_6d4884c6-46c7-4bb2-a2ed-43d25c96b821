import { TinputField, TFunction } from "@/types";
import { EmployeeSchemaType } from "@/app/[locale]/_modules/people/schemas/employeeSchema";
import { SalarySchemaType } from "@/app/[locale]/_modules/people/schemas/salarySchema";
import { SalaryPackageSchemaType } from "@/app/[locale]/_modules/people/schemas/salaryPackageSchema";
import { CreateLeaveSchemaType } from "@/app/[locale]/_modules/people/schemas/leaveSchema";
import { DEPARTMENT_OPTIONS } from "@/app/[locale]/_modules/people/constants/employee";
import {
  LEAVE_DURATION,
  LEAVE_TYPE,
} from "@/app/[locale]/_modules/people/enum";
import { capitalize } from "lodash";

const getDepartmentTranslation = (department: string, t: TFunction) => {
  try {
    // Try to get the translation
    return t(
      `people.employees-page.add-employee-dialog.form.departments.${department}`,
    );
  } catch (error) {
    // If translation is missing, return capitalized department name
    return capitalize(department);
  }
};

export const getUpdateTotalSalaryForm = (
  t: TFunction,
): TinputField<SalarySchemaType>[] => [
  // Helper function to get department translations with fallbacks
  {
    name: "totalSalary",
    type: "text",
    className: " placeholder:text-disabled-text",
    labelClassName: "block",
    label: t(
      "people.employees-salaries-page.edit-salary-dialog.content.input-label",
    ),
    placeholder: "23,960",
    formatOnChange: true,
  },
  {
    name: "note",
    type: "textarea",
    className:
      "w-full rounded-[10px] min-h-[138px] max-h-[138px] placeholder:text-gray-400",
    label: t("people.leaves-requests-page.dialog-common.note-label"),
    labelClassName: "block  pt-5",
    placeholder: t(
      "people.employees-salaries-page.edit-salary-dialog.content.note-placeholder",
    ),
  },
];

export const getAddEmployeeForm = (
  t: TFunction,
): TinputField<EmployeeSchemaType>[] => [
  {
    name: "avatar",
    type: "file",
    className: "w-4 h-4",
    label: "",
    placeholder: t(
      "people.employees-page.add-employee-dialog.form.profile-image.placeholder",
    ),
    avatarSize: "sm",
    containerClassName: "gap-1",
    changeTranslationKey: "common.form.profile-image.buttons.upload",
    changeButtonIcon: "plus",
    addBtnClassName:
      "bg-transparent shadow-none hover:bg-transparent text-secondary font-bold !text-sm underline border-e ",
    deleteBtnClassName: "border-none text-sm font-bold underline",
    defaultImage: "/images/icons/profile-placeholder.svg",
    deleteTranslationKey: "common.form.profile-image.buttons.delete",
    fallbackText: "U",
  },
  {
    name: "name",
    className: "pt-16",
    type: "text",
    label: t(
      "people.employees-page.add-employee-dialog.form.employee-name.label",
    ),
    placeholder: t(
      "people.employees-page.add-employee-dialog.form.employee-name.placeholder",
    ),
    rightIcon: "Profile",
  },
  {
    name: "email",
    type: "email",
    label: t("people.employees-page.add-employee-dialog.form.email.label"),
    placeholder: t(
      "people.employees-page.add-employee-dialog.form.email.placeholder",
    ),
  },
  {
    name: "phone",
    type: "tel",
    label: t("people.employees-page.add-employee-dialog.form.mobile.label"),
    placeholder: t(
      "people.employees-page.add-employee-dialog.form.mobile.placeholder",
    ),
    rightIcon: "Profile",
  },
  {
    name: "start_date",
    type: "date",
    label: t(
      "people.employees-page.add-employee-dialog.form.registration-date.label",
    ),
    placeholder: t(
      "people.employees-page.add-employee-dialog.form.registration-date.placeholder",
    ),
    className: "w-full",
  },
  {
    name: "department",
    className: "text-base font-normal !text-gray-500",
    type: "select",
    label: t("people.employees-page.add-employee-dialog.form.department.label"),
    placeholder: t(
      "people.employees-page.add-employee-dialog.form.department.placeholder",
    ),
    options: DEPARTMENT_OPTIONS.map((department) => ({
      value: department,
      label: getDepartmentTranslation(department, t),
    })),
  },
  {
    name: "assignments",
    type: "assignments",
    label: t(
      "people.employees-page.add-employee-dialog.form.assignments.label",
    ),
    containerClassName: "mt-6",
  },
  {
    name: "attachments",
    type: "attachments",
    label: t(
      "people.employees-page.add-employee-dialog.form.attachments.label",
    ),
    labelClassName: "sr-only",
    placeholder: t(
      "people.employees-page.add-employee-dialog.form.attachments.placeholder",
    ),
    accept: "*/*",
    multiple: true,
    maxFiles: 10,
    maxSize: 5000000, // 5MB
    containerClassName: "mt-2",
    buttonClassName: "w-full justify-center text-secondary",
    uploadButtonText: t(
      "people.employees-page.add-employee-dialog.form.attachments.button",
    ),
  },
];

export const getCreateLeaveForm = (
  t: TFunction,
): TinputField<CreateLeaveSchemaType>[] => [
  {
    name: "leave_type",
    type: "select",
    label: t(
      "people.employees-page.profile.leaves.create-leave-modal.leave-type",
    ),
    placeholder: t(
      "people.employees-page.profile.leaves.create-leave-modal.select-leave-type",
    ),
    options: Object.values(LEAVE_TYPE).map((leaveType) => ({
      value: leaveType,
      label: t(
        `people.employees-page.profile.leaves.table.leave-types.${leaveType}`,
      ),
    })),
  },
  {
    name: "leave_duration",
    type: "select",
    label: t(
      "people.employees-page.profile.leaves.create-leave-modal.leave-duration",
    ),
    placeholder: t(
      "people.employees-page.profile.leaves.create-leave-modal.select-leave-duration",
    ),
    options: Object.values(LEAVE_DURATION).map((leaveDuration) => ({
      value: leaveDuration,
      label: t(
        `people.employees-page.profile.leaves.table.leave-durations.${leaveDuration}`,
      ),
    })),
  },
  {
    name: "start_date",
    type: "date",
    label: t(
      "people.employees-page.profile.leaves.create-leave-modal.start-date",
    ),
    className: "w-full",
  },
  {
    name: "end_date",
    type: "date",
    label: t(
      "people.employees-page.profile.leaves.create-leave-modal.end-date",
    ),
    className: "w-full",
  },
  {
    name: "reason",
    type: "textarea",
    label: t("people.employees-page.profile.leaves.create-leave-modal.reason"),
    placeholder: t(
      "people.employees-page.profile.leaves.create-leave-modal.reason-placeholder",
    ),
    className: "w-full min-h-[100px]",
  },
  {
    name: "documents",
    type: "attachments",
    label: t(
      "people.employees-page.profile.leaves.create-leave-modal.attachments",
    ),
    placeholder: t(
      "people.employees-page.profile.leaves.create-leave-modal.attachments-placeholder",
    ),
    accept: "*/*",
    multiple: true,
    maxFiles: 10,
    maxSize: 5000000, // 5MB
    containerClassName: "mt-2",
    buttonClassName: "w-full justify-center text-secondary",
    uploadButtonText: t(
      "people.employees-page.profile.leaves.create-leave-modal.upload-attachments",
    ),
  },
];

export const getCreateSalaryPackageForm = (
  t: TFunction,
): TinputField<SalaryPackageSchemaType>[] => [
  {
    name: "base_salary",
    type: "text",
    className: "placeholder:text-disabled-text",
    labelClassName: "block",
    label: t("common.form.salary.base_salary.label"),
    placeholder: t("common.form.salary.base_salary.placeholder"),
    formatOnChange: true,
    formatOnBlur: true,
  },
  {
    name: "housing_allowance",
    type: "text",
    className: "placeholder:text-disabled-text",
    labelClassName: "block",
    label: t("common.form.salary.housing_allowance.label"),
    placeholder: t("common.form.salary.housing_allowance.placeholder"),
    formatOnChange: true,
    formatOnBlur: true,
  },
  {
    name: "transportation_allowance",
    type: "text",
    className: "placeholder:text-disabled-text",
    labelClassName: "block",
    label: t("common.form.salary.transportation_allowance.label"),
    placeholder: t("common.form.salary.transportation_allowance.placeholder"),
    formatOnChange: true,
    formatOnBlur: true,
  },
  {
    name: "other_allowances",
    type: "text",
    className: "placeholder:text-disabled-text",
    labelClassName: "block",
    label: t("common.form.salary.other_allowances.label"),
    placeholder: t("common.form.salary.other_allowances.placeholder"),
    formatOnChange: true,
    formatOnBlur: true,
  },
  {
    name: "effective_date",
    type: "date",
    label: t("common.form.salary.effective_date.label"),
    className: "w-full",
  },
  {
    name: "notes",
    type: "textarea",
    className:
      "w-full rounded-[10px] min-h-[138px] max-h-[138px] placeholder:text-gray-400",
    label: t("common.form.salary.notes.label"),
    labelClassName: "block pt-5",
    placeholder: t("common.form.salary.notes.placeholder"),
  },
];
