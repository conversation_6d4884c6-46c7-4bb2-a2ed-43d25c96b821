import { TFunction, TinputField } from "@/types";
import { FieldValues } from "react-hook-form";
import { getFormFieldsByType } from "./form-fields";

const useFormFields = <T extends FieldValues>({
  formType,
  t,
}: {
  formType: string;
  t: TFunction;
}) => {
  const getFormFields = (): TinputField<T>[] => {
    return getFormFieldsByType<T>(formType, t);
  };

  return {
    getFormFields,
  };
};

export default useFormFields;
