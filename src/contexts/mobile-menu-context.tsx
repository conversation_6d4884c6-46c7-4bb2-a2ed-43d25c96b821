"use client";

import React, {
  createContext,
  useContext,
  useState,
  ReactNode,
  useEffect,
} from "react";
import useMediaQuery from "@/hooks/use-media-query";

interface MobileMenuContextProps {
  isMenuOpen: boolean;
  openMenu: () => void;
  closeMenu: () => void;
  toggleMenu: () => void;
  isMobileScreen: boolean;
}

const MobileMenuContext = createContext<MobileMenuContextProps | undefined>(
  undefined,
);

export const MobileMenuProvider = ({ children }: { children: ReactNode }) => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  // Use true as default for mobile screens to ensure consistent rendering during hydration
  const isMobileScreen = useMediaQuery("(max-width: 768px)", true);

  // Close menu when screen size changes to desktop
  useEffect(() => {
    if (!isMobileScreen && isMenuOpen) {
      setIsMenuOpen(false);
    }
  }, [isMobileScreen, isMenuOpen]);

  // Prevent scrolling when menu is open
  useEffect(() => {
    if (typeof document === "undefined") return;

    if (isMenuOpen) {
      document.body.classList.add("overflow-hidden");
    } else {
      document.body.classList.remove("overflow-hidden");
    }

    return () => document.body.classList.remove("overflow-hidden");
  }, [isMenuOpen]);

  const openMenu = () => isMobileScreen && setIsMenuOpen(true);
  const closeMenu = () => setIsMenuOpen(false);
  const toggleMenu = () => isMobileScreen && setIsMenuOpen((prev) => !prev);

  return (
    <MobileMenuContext.Provider
      value={{
        isMenuOpen,
        openMenu,
        closeMenu,
        toggleMenu,
        isMobileScreen,
      }}
    >
      {children}
    </MobileMenuContext.Provider>
  );
};

export const useMobileMenu = (): MobileMenuContextProps => {
  const context = useContext(MobileMenuContext);
  if (context === undefined) {
    throw new Error("useMobileMenu must be used within a MobileMenuProvider");
  }
  return context;
};
