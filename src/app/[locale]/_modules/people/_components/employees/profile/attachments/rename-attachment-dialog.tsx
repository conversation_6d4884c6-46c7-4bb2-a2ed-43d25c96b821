"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { DialogTitle } from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import ResponsiveDialog from "@/components/responsive-dialog";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { useTranslations } from "next-intl";
import { useEffect, useCallback } from "react";
import { z } from "zod";
import { TEmployeeAttachment } from "../../../../type/employee";
import { LoaderCircle } from "lucide-react";

type RenameAttachmentDialogProps = {
  attachment: TEmployeeAttachment | null;
  isOpen: boolean;
  onClose: () => void;
  onRename: (attachmentId: string, newName: string) => Promise<boolean>;
  isRenaming: boolean;
};

export default function RenameAttachmentDialog({
  attachment,
  isOpen,
  onClose,
  onRename,
  isRenaming,
}: RenameAttachmentDialogProps) {
  const t = useTranslations();

  // Create schema for renaming
  const renameSchema = z.object({
    name: z
      .string()
      .min(1, t("common.form.attachment.error.name.required"))
      .max(100, t("common.form.attachment.error.name.maxLength")),
  });

  type RenameFormValues = z.infer<typeof renameSchema>;

  // Get file extension from the original name
  const getFileExtension = useCallback((filename: string) => {
    const parts = filename.split(".");
    return parts.length > 1 ? parts.pop() : "";
  }, []);

  // Get file name without extension
  const getFileNameWithoutExtension = useCallback(
    (filename: string) => {
      const extension = getFileExtension(filename);
      if (extension) {
        return filename.slice(0, -(extension.length + 1)); // +1 for the dot
      }
      return filename;
    },
    [getFileExtension],
  );

  // Initialize form with empty default value
  const form = useForm<RenameFormValues>({
    resolver: zodResolver(renameSchema),
    defaultValues: {
      name: "",
    },
  });

  // Update form value when attachment changes or dialog opens
  useEffect(() => {
    if (attachment && isOpen) {
      const nameWithoutExtension = getFileNameWithoutExtension(
        attachment.attributes.filename,
      );
      form.setValue("name", nameWithoutExtension);
    }

    if (!isOpen) {
      form.reset();
    }
  }, [attachment, isOpen, form, getFileNameWithoutExtension]);

  const currentName = form.watch("name");
  const isEmpty = !currentName || currentName.trim() === "";

  const { isDirty } = form.formState;

  // Handle form submission
  const onSubmit = async (values: RenameFormValues) => {
    if (!attachment) return;

    // Add the original extension back to the new name
    const extension = getFileExtension(attachment.attributes.filename);
    const newName = extension ? `${values.name}.${extension}` : values.name;

    const success = await onRename(attachment.id, newName);
    if (success) {
      onClose();
    }
  };

  const handleClose = () => {
    form.reset();
    onClose();
  };

  return (
    <ResponsiveDialog
      open={isOpen}
      onOpenChange={handleClose}
      closeBtnStyle="top-[22px]"
      header={
        <DialogTitle className="px-6 py-[22px] border-b font-semibold text-[18px] leading-[28px]">
          {t("people.employees-page.profile.attachments.rename-dialog.title")}
        </DialogTitle>
      }
    >
      <div className="space-y-4">
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <Input
                      {...field}
                      placeholder={t(
                        "people.employees-page.profile.attachments.rename-dialog.placeholder",
                      )}
                      className="h-12"
                      disabled={isRenaming}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <div className="flex justify-end gap-3 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={handleClose}
                disabled={isRenaming}
                className="h-12 px-6"
              >
                {t("common.buttonText.cancel")}
              </Button>
              <Button
                type="submit"
                disabled={
                  isRenaming || !isDirty || isEmpty || !form.formState.isValid
                }
                className="h-12 px-6"
              >
                {isRenaming ? (
                  <>
                    <LoaderCircle className="mr-2 h-4 w-4 animate-spin" />
                    {t("common.buttonText.saving")}
                  </>
                ) : (
                  t("common.buttonText.save")
                )}
              </Button>
            </div>
          </form>
        </Form>
      </div>
    </ResponsiveDialog>
  );
}
