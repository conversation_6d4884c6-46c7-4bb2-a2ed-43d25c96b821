"use client";

import { useTranslations } from "next-intl";
import { MetricCardsSkeleton } from "../../../../skeletons/metric-card-skeleton";
import { MetricCard } from "../../../common/metric-card";
import { format, subDays } from "date-fns";
import { useGetDayStatistic } from "../../../../hooks/employees/useEmployeeDaySummary";
import { TimeCardSkeleton } from "../../../../skeletons/time-card-skeleton";
import { TimeCard } from "../../../common/timeCard";
import { useStatistic } from "../../../../hooks/employees/useEmployeeStatistics";
import { MetricCardTypes } from "@/enums/statistics";

type EmployeeAttendanceCardsProps = {
  employeeId: string;
};

export const EmployeeAttendanceCards = ({
  employeeId,
}: EmployeeAttendanceCardsProps) => {
  const t = useTranslations();

  const getDate = (offset: number) => {
    const date = subDays(new Date(), offset);
    return format(date, "dd-MM-yyyy");
  };

  const dayStats = [
    { label: "today", ...useGetDayStatistic(employeeId, getDate(0)) },
    { label: "yesterday", ...useGetDayStatistic(employeeId, getDate(1)) },
    { label: "beforeYesterday", ...useGetDayStatistic(employeeId, getDate(2)) },
  ];

  const { metricCard, isLoading, error } = useStatistic(employeeId, [
    MetricCardTypes.Leaves,
    MetricCardTypes.LateArrivals,
    MetricCardTypes.EarlyDepartures,
    MetricCardTypes.Absences,
  ]);

  const anyError = dayStats.some((d) => d.error) || error;
  if (anyError) {
    const errorMessage =
      dayStats.find((d) => d.error)?.error?.message ||
      error?.message ||
      "Error loading data";

    return (
      <div className="text-error p-4 min-h-[148px] border rounded-[20px] flex justify-center items-center">
        {errorMessage}
      </div>
    );
  }
  return (
    <>
      {metricCard && !isLoading ? (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-5">
          {metricCard.map((metric, index) => {
            return (
              <MetricCard
                key={metric.id}
                title={metric.attributes.title}
                value={`${metric.attributes.value}`}
                percentageChange={metric.attributes.comparison.percentage}
                comparisonText={metric.attributes.comparison.text}
                key={`metric-card-${index}`}
              />
            );
          })}
        </div>
      ) : (
        <MetricCardsSkeleton count={4} />
      )}

      <div className="grid grid-cols-1 gap-4 mt-5">
        {dayStats.map((stat, idx) => (
          <div key={`day-stat-${idx}`}>
            {stat.isLoading ? (
              <TimeCardSkeleton />
            ) : (
              stat.meta && (
                <TimeCard
                  startTime={stat.meta.summary?.arrival_time}
                  endTime={stat.meta.summary?.departure_time}
                  total_work_minutes={stat.meta.summary?.minutes_spent_working}
                  periods={stat.attendanceStats}
                  date={stat.meta.date}
                />
              )
            )}
          </div>
        ))}
      </div>
    </>
  );
};
