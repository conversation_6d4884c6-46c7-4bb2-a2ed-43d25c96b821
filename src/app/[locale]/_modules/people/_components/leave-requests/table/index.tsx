"use client";

import { DataTable } from "@/components/table";
import React, { useState } from "react";
import { columns } from "./leaves-requests-columns";
import { useLocale, useTranslations } from "next-intl";
import { PaginationWithLinks } from "@/components/pagination-with-links";
import { RowSelectionState } from "@tanstack/react-table";
import dynamic from "next/dynamic";
import Loader from "@/components/loader";
// import { useToast } from "@/hooks/use-toast";
import { useLeaveRequestMutations } from "../../../hooks/useLeaveRequestMutations";
import { useSearchParams } from "next/navigation";
import { LeaveDetail } from "../../../type/employee-leaves";
import { TEmployee } from "../../../type/employee";

// const EmployeeNoteDialog = dynamic(() => import("../employee-note-dialog"), {
//   loading: () => <Loader overlayColor="#000" overlayOpacity={0.6} />,
//   ssr: false,
// });

const EmployeeRequestsDetailsDialog = dynamic(
  () => import("../employee-requests-details-dialog"),
  {
    loading: () => <Loader overlayColor="#000" overlayOpacity={0.6} />,
    ssr: false,
  },
);

// const EmployeeRequestApprovalDialog = dynamic(
//   () => import("../employee-request-approval-dialog"),
//   {
//     loading: () => <Loader overlayColor="#000" overlayOpacity={0.6} />,
//     ssr: false,
//   },
// );

const LeaveRequestsTable = ({
  showPagination = false,
  searchParams: serverSearchParams,
}: {
  showPagination?: boolean;
  searchParams?: { page: string; limit: string };
}) => {
  const t = useTranslations();
  const locale = useLocale();
  const clientSearchParams = useSearchParams();

  // Use server-provided params if available, otherwise fall back to client-side params
  const limit =
    serverSearchParams?.limit ?? clientSearchParams.get("limit") ?? "5";
  const page =
    serverSearchParams?.page ?? clientSearchParams.get("page") ?? "1";
  const [rowSelection, setRowSelection] = useState<RowSelectionState>({});
  const [showRequestData, setShowRequestData] = useState(false);
  // const [showApprovalModal, setShowApprovalModal] = useState(false);
  const [selectedEmployeeRequest, setSelectedEmployeeRequest] = useState<{
    employee: TEmployee | null;
    leaves: LeaveDetail | null;
  }>({
    employee: null,
    leaves: null,
  });

  // const [showNoteDialog, setshowNoteDialog] = useState(false);
  // const { toast } = useToast();
  const searchQuery = clientSearchParams.get("search") || "";
  const {
    data: requests,
    isLoading,
    error,
    mutateData,
    isApproving,
    isRejecting,
    isWithdrawing,
  } = useLeaveRequestMutations({
    key: `/api/leaves?page=${page}&limit=${limit}&sort=updated_at&include=employee
    ${searchQuery ? `&search=${encodeURIComponent(searchQuery)}` : ""}
    `,
  });

  const totalCount = requests?.meta?.pagination?.count || 0;

  const firstResult = requests.meta.pagination.from || 1;
  const lastResult = requests.meta.pagination.to || 1;

  // Set the selected patient and show the modal
  const handleShowPatientDetails = (data: LeaveDetail, employee: TEmployee) => {
    setSelectedEmployeeRequest({ employee: employee, leaves: data });
    setShowRequestData(true);
  };
  // const handleShowNoteDialog = (data: LeaveDetail, employee: TEmployee) => {
  //   setSelectedEmployeeRequest({ employee: employee, leaves: data });
  //   setshowNoteDialog(true);
  // };
  /* we hide note handling temporarily
  const handleAddNote = (note: string) => {
    // Close the dialog after adding the note
    toast({
      className: "toast-success",
      description: "successfully added the note",
    });
    setshowNoteDialog(false);
    setShowApprovalModal(false);
  };
*/
  const handleAcceptRequest = (data: LeaveDetail) => {
    mutateData("acceptLeaveRequest", {
      id: data.relationships.approval_request.data.id,
      leaveId: data.id,
    });
  };

  const handleRejectRequest = (data: LeaveDetail) => {
    mutateData("rejectLeaveRequest", {
      id: data.relationships.approval_request.data.id,
      leaveId: data.id,
    });
  };

  const handleWithdrawRequest = (data: LeaveDetail) => {
    mutateData("withdrawLeaveRequest", {
      id: data.id,
      employeeId: data.relationships.employee.data.id,
      leaveId: data.id,
    });
  };

  return (
    <>
      <DataTable
        data={requests.data || []}
        columns={columns}
        tableContainerClass="min-h-[240px]"
        title={t(`people.leaves-requests-page.table.title`)}
        meta={{
          t,
          locale,
          onShowDetails: handleShowPatientDetails,
          // onShowNote: handleShowNoteDialog, //hide temp
          onAcceptRequest: handleAcceptRequest,
          onRejectRequest: handleRejectRequest,
          employeeData: requests.included,
        }}
        rowSelection={rowSelection}
        onRowSelectionChange={setRowSelection}
        translationPrefix="people.leaves-requests-page.table"
        isLoading={isLoading}
        initialLimit={5}
        error={error}
        dataCount={totalCount}
      />
      {showPagination && (
        <div className="w-full pt-[18px]">
          <PaginationWithLinks
            page={Number(page)}
            pageSize={Number(limit)}
            totalCount={Number(totalCount)}
            firstLastCounts={{
              firstCount: firstResult ?? 1,
              lastCount: lastResult ?? 3,
            }}
            isLoading={isLoading}
            isDisabled={!requests?.data?.length}
            pageSizeSelectOptions={{
              pageSizeOptions: [5, 10, 25, 30, 45, 50],
              pageSizeSearchParam: "limit",
            }}
          />
        </div>
      )}
      {showRequestData && (
        <EmployeeRequestsDetailsDialog
          employeeRequestData={selectedEmployeeRequest}
          showRequestData={showRequestData}
          setShowRequestData={setShowRequestData}
          onAcceptRequest={handleAcceptRequest}
          onRejectRequest={handleRejectRequest}
          onWithdrawRequest={handleWithdrawRequest}
          isApproving={isApproving}
          isRejecting={isRejecting}
          isWithdrawing={isWithdrawing}
        />
      )}
      {/* {showNoteDialog && (
        <EmployeeNoteDialog
          showNoteDialog={showNoteDialog}
          setShowNoteDialog={setshowNoteDialog}
          onAddNote={handleAddNote}
        />
      )} */}
      {/* {showApprovalModal && (
        <EmployeeRequestApprovalDialog
          showApprovalModal={showApprovalModal}
          setShowApprovalModal={setShowApprovalModal}
          onAddNote={handleAddNote}
        />
      )} */}
    </>
  );
};

export default LeaveRequestsTable;
