"use client";

import { useTranslations } from "next-intl";
import { useEffect } from "react";

type Props = {
  error: Error;
  reset(): void;
};

export default function Error({ error, reset }: Props) {
  const t = useTranslations("common.Error");

  useEffect(() => {
    // Only log errors if not in production or if NEXT_SUPPRESS_ERRORS is not set to true
    if (process.env.NODE_ENV !== 'production' || process.env.NEXT_SUPPRESS_ERRORS !== 'true') {
      console.error(error);
    }
  }, [error]);

  return (
    <div className="flex flex-col items-center justify-center min-h-screen bg-[hsl(var(--background-v2))] px-6">
      <div className="bg-[hsl(var(--card))] shadow-lg rounded-[var(--radius)] p-8 max-w-md text-center border border-[hsl(var(--border))]">
        <h1 className="text-2xl font-bold text-[hsl(var(--error))]">
          {t("title")}
        </h1>
        <div className="mt-4 text-[hsl(var(--foreground))]">
          {t.rich("description", {
            p: (chunks) => <p className="mt-4">{chunks}</p>,
            retry: (chunks) => (
              <button
                className="mt-4 px-4 py-2 bg-[hsl(var(--secondary))] text-[hsl(var(--primary-foreground))] font-medium rounded-md hover:brightness-90 transition-all"
                onClick={reset}
                type="button"
              >
                {chunks}
              </button>
            ),
          })}
        </div>
      </div>
    </div>
  );
}
