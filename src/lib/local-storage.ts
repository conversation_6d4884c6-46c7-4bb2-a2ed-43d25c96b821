"use client";

export const saveDataToLocalStorage = (key: string, value: any): void => {
  if (typeof window !== "undefined") {
    try {
      localStorage.setItem(key, JSON.stringify(value));
    } catch (error) {
      console.error("Error saving to localStorage:", error);
    }
  }
};

export const saveDataToSessionStorage = (key: string, value: any): void => {
  if (typeof window !== "undefined") {
    try {
      sessionStorage.setItem(key, JSON.stringify(value));
    } catch (error) {
      console.error("Error saving to sessionStorage:", error);
    }
  }
};

export const getDataFromLocalStorage = <T = unknown>(key: string): T | null => {
  if (typeof window !== "undefined") {
    try {
      const data = localStorage.getItem(key);
      return data ? (JSON.parse(data) as T) : null;
    } catch (error) {
      console.error("Error getting data from localStorage:", error);
      return null;
    }
  }
  return null;
};

export const getDataFromSessionStorage = <T = unknown>(
  key: string,
): T | null => {
  if (typeof window !== "undefined") {
    try {
      const data = sessionStorage.getItem(key);
      return data ? (JSON.parse(data) as T) : null;
    } catch (error) {
      console.error("Error getting data from sessionStorage:", error);
      return null;
    }
  }
  return null;
};
