export const downloadFile = (
  fileUrl: string,
  fileName?: string,
  openInNewTab: boolean = false,
): void => {
  if (openInNewTab) {
    window.open(fileUrl, "_blank");
    return;
  }
  // Default download behavior
  const link = document.createElement("a");
  link.href = fileUrl;
  link.download = fileName || fileUrl.split("/").pop() || "download"; // Default fallback name
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};
