import axios from "axios";

export interface CustomErrorInterface extends <PERSON><PERSON>r {
  statusCode: number;
}

// HttpError class implementing the interface
export class HttpError extends Error implements CustomErrorInterface {
  public statusCode: number;

  constructor(message: string, statusCode: number) {
    super(message);
    this.name = "HttpError";
    this.statusCode = statusCode;

    // Ensure proper prototype chain for instanceof checks
    Object.setPrototypeOf(this, HttpError.prototype);
  }
}

// Utility function to normalize errors into HttpError
export function normalizeError(error: unknown): HttpError {
  if (error instanceof HttpError) {
    return error; // Return as-is if it's already an HttpError
  }
  if (axios.isAxiosError(error)) {
    return new HttpError(error.message, error.response?.status ?? 500);
  }
  // Fallback for unknown errors
  const message = error instanceof Error ? error.message : "Unknown error";
  return new HttpError(message, 500);
}

// Optional: Type guard to check if an error is an HttpError
export function isHttpError(error: unknown): error is HttpError {
  return error instanceof HttpError;
}
