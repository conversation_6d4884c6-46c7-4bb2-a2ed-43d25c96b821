import { format, fromZonedTime, toZonedTime } from "date-fns-tz";

export const to24HourFormat = (
  hour: string,
  minute: string,
  period: string
): string => {
  let h = parseInt(hour, 10);
  if (period.toUpperCase() === "PM" && h !== 12) {
    h += 12;
  }
  if (period.toUpperCase() === "AM" && h === 12) {
    h = 0;
  }
  return `${h.toString().padStart(2, "0")}:${minute}`;
};

export const convertTo12Hour = (
  time24: string,
  locale: string = "en"
): { hour: string; minute: string; period: string } => {
  const [hoursStr, minutes] = time24.split(":");
  const hours = parseInt(hoursStr, 10);
  const period =
    hours >= 12
      ? locale === "ar"
        ? "مساءا"
        : "PM"
      : locale === "ar"
      ? "صباحا"
      : "AM";
  let hour = hours % 12;
  if (hour === 0) hour = 12;
  return { hour: String(hour), minute: minutes, period };
};

// time zone
export function convertUTCToLocalTime(
  utcTimeStr: string,
  timeZone: string,
  formatStr = "HH:mm"
): string {
  const utcDate = new Date(utcTimeStr);
  const localDate = toZonedTime(utcDate, timeZone);
  return format(localDate, formatStr, { timeZone });
}

export function convertTimeWithoutDateToUTC(
  timeStr: string,
  timeZone: string
): string {
  // Use a dummy date for conversion
  const today = new Date().toISOString().slice(0, 10);
  const dateTimeStr = `${today}T${timeStr}:00`;
  const localDate = new Date(dateTimeStr);
  const utcDate = fromZonedTime(localDate, timeZone);
  return utcDate.toISOString();
}
