import { ActionState, TFunction } from "@/types";
import { getTranslations } from "next-intl/server";

import { z } from "zod";
import { handleError } from "./utils";

export function isValidationSuccess<T>(
  validation: { success: true; data: T } | ActionState<T>,
): validation is { success: true; data: T } {
  return "success" in validation && validation.success === true;
}

export async function validateFormData<T>(
  data: FormData,
  schema: (t: TFunction) => z.ZodSchema<T>,
): Promise<{ success: true; data: T } | ActionState<T>> {
  // Convert FormData to an object
  const formData = Object.fromEntries(data);

  // We don't need special date processing anymore since our schemas handle string-to-date conversion

  const t = (await getTranslations()) as TFunction;
  const results = schema(t).safeParse(formData);

  if (!results.success) {
    return handleError(
      results.error,
      "Invalid form data",
      results.error.issues.map((issue) => issue.message),
    );
  }

  return { success: true, data: results.data as T };
}
