import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";
import { ActionState, TSystem } from "@/types";
import { TUserSystem } from "@/types/auth";
import { PhoneNumberUtil } from "google-libphonenumber";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export const filterSystems = (
  systems: TSystem[],
  userAvailableSystems: TUserSystem[],
): TSystem[] => {
  return systems.filter((system) => {
    return userAvailableSystems.some(
      (systems) => system.name === systems.alias,
    );
  });
};

// Helper function to handle errors
export const handleError = <T>(
  err: unknown,
  fallback: string,
  issues?: string[],
): ActionState<T> => {
  const errorMsg = err instanceof Error ? err.message : fallback;
  return {
    error: errorMsg,
    success: "",
    issues: issues || [],
    data: null as T | null,
  };
};
const phoneUtil = PhoneNumberUtil.getInstance();
export const validatePhoneNumber = (phoneNumber: string): boolean => {
  try {
    const phoneNumberWithPlus = phoneNumber.startsWith("+")
      ? phoneNumber
      : `+${phoneNumber}`;
    const parsedPhoneNumber = phoneUtil.parse(phoneNumberWithPlus);
    const countryCode = phoneUtil.getRegionCodeForNumber(parsedPhoneNumber);
    const isValidForRegion = phoneUtil.isValidNumberForRegion(
      parsedPhoneNumber,
      countryCode,
    );

    const isValid = phoneUtil.isValidNumber(parsedPhoneNumber);
    const isPossible = phoneUtil.isPossibleNumber(parsedPhoneNumber);

    return isValid && isPossible && isValidForRegion;
  } catch (error) {
    return false;
  }
};

export const createDateWithoutTimezoneIssue = (
  date: string | Date | undefined,
) => {
  if (!date) return undefined;

  // If it's already a Date object, preserve it
  if (date instanceof Date) return date;

  // If it's a string with time part, use the standard constructor
  if (typeof date === "string" && date.includes("T")) {
    return new Date(date);
  }

  // For date strings without time, create a date at noon to avoid timezone issues
  const parsedDate = new Date(date);
  return new Date(
    parsedDate.getFullYear(),
    parsedDate.getMonth(),
    parsedDate.getDate(),
    12,
    0,
    0,
  );
};
