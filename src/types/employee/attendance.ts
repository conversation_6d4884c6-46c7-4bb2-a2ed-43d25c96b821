export type AttendanceSegment = {
  type: "late" | "work" | "break" | "overtime";
  start_time: string;
  end_time: string;
  duration_minutes: number;
  description: string;
  break_type?: "lunch" | "regular";
};

export const typeToColor: Record<
  AttendanceSegment["type"],
  "green" | "blue" | "orange" | "red" | "gray"
> = {
  late: "orange",
  work: "green",
  break: "blue",
  overtime: "red",
};
