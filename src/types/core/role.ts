export type TRoleAttributes = {
  name: string;
  description?: string;
  status?: string;
  scope?: string;
};

export type TRoleRelationship = {
  id: string;
  type: string;
};

export type TRoleRelationships = {
  projects?: {
    data: TRoleRelationship[];
  };
  users?: {
    data: TRoleRelationship[];
  };
};

export type TRole = {
  id: string;
  type: string;
  attributes: TRoleAttributes;
  relationships?: TRoleRelationships;
};

export type TRolesResponse = {
  data: TRole[];
  meta: {
    pagination: {
      count: number;
      page: number;
      limit: number;
      from: number;
      to: number;
    };
  };
};
