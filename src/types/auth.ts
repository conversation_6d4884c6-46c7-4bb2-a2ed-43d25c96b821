import { TSystems, ApiResponse } from ".";

export type LoginResponse = {
  message: string;
  user: TUser;
  main_token: string;
};

export type SessionResponse = {
  message: string;
  scope: TSystems;
  session_token: string;
};

export type TUser = {
  id: string;
  name: string;
  email: string;
  status?: string;
  avatar?: string;
  roles?: string[];
  systems?: TUserSystem[];
};

export type TUserSystem = {
  name: string;
  alias: string;
  display_name: string;
  description: string;
};

export type UserMeta = {
  allowed_systems: TUserSystem[];
};

export type ApiUserResponse = ApiResponse<TUser, UserMeta>;

export type LoginFormData = {
  email: string;
  password: string;
};

export type ForgotPasswordFormData = {
  email: string;
};

export type ResetPasswordFormData = {
  password: string;
  confirmPassword: string;
  token: string;
};

export type VerifyCodeFormData = {
  code: string;
};

export interface PermissionAttributes {
  action: string;
  subject: string;
  permission: string;
}

export interface Permission {
  id: string;
  type: string;
  attributes: PermissionAttributes;
}
