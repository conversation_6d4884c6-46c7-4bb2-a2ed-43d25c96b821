import { TFunction } from "@/types";
import { z } from "zod";

export const changePasswordSchema = (t: TFunction) => {
  return z
    .object({
      currentPassword: z
        .string()
        .nonempty(t("common.form.password.error.currRequired")),
      password: z
        .string()
        .nonempty(t("common.form.password.error.required"))
        .min(6, t("common.form.password.error.length")),
      confirmPassword: z
        .string()
        .nonempty(t("common.form.confirmPassword.error.required"))
        .min(6, t("common.form.password.error.length")),
    })
    .refine((obj) => obj.password === obj.confirmPassword, {
      message: t("common.form.password.error.match"),
      path: ["confirmPassword"],
    });
};

export type changePasswordSchemaType = z.infer<
  ReturnType<typeof changePasswordSchema>
>;

const MAX_FILE_SIZE = 2000000;
const ACCEPTED_IMAGE_TYPES = [
  "image/jpeg",
  "image/jpg",
  "image/png",
  "image/webp",
];

export const editProfileSchema = (t: TFunction) => {
  const fileSchema = z
    .instanceof(File)
    .refine((file) => file.size < MAX_FILE_SIZE, {
      message: t("common.form.profile-image.error.file.maxSize"),
    })
    .refine((file) => ACCEPTED_IMAGE_TYPES.includes(file.type), {
      message: t("common.form.profile-image.error.file.acceptedTypes"),
    });
  return z.object({
    name: z
      .string()
      .max(25, { message: t("common.form.name.error.maxLength") })
      .optional(),
    email: z.string().nullable().optional(),
    password: z.string().nullable().optional(),
    avatar: z.union([z.string().optional(), fileSchema, z.null()]).optional(),
  });
};

export type editProfileSchemaType = z.infer<
  ReturnType<typeof editProfileSchema>
>;
