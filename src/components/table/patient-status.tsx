import { PATIENT_STATUS } from "@/constants/enum";

const statusBackgroundColors = {
  [PATIENT_STATUS.INCOMING]: "bg-success-50",
  [PATIENT_STATUS.PENDING]: "bg-hang-50",
  [PATIENT_STATUS.WAITING]: "bg-wait-50",
  [PATIENT_STATUS.COMPLETED]: "bg-success-50",
};

const statusTextColors = {
  [PATIENT_STATUS.INCOMING]: "text-success",
  [PATIENT_STATUS.PENDING]: "text-hang",
  [PATIENT_STATUS.WAITING]: "text-wait",
  [PATIENT_STATUS.COMPLETED]: "text-success",
};

const PatientStatus = ({
  status,
  label,
}: {
  status:
    | PATIENT_STATUS.INCOMING
    | PATIENT_STATUS.PENDING
    | PATIENT_STATUS.WAITING
    | PATIENT_STATUS.COMPLETED;
  label: string;
}) => {
  return (
    <div
      className={`min-w-24 max-w-24 mx-auto min-h-[27px] leading-[27px] px-3 rounded-sm text-sm font-normal text-start ${statusBackgroundColors[status]} ${statusTextColors[status]}`}
    >
      {label}
    </div>
  );
};

export default PatientStatus;
