import { Table } from "@tanstack/react-table";
import { TFilterGroup, FilterRule, FilterableColumn } from "../types";
import { TFunction } from "@/types";

// Map of table translation prefixes to manually defined filterable columns
const tableFilterColumns: Record<string, FilterableColumn[]> = {
  // Employees table
  "people.employees-page.table": [
    { id: "employeeName", filterType: "text" },
    { id: "registrationDate", filterType: "date" },
    { id: "department", filterType: "text" },
  ],
};

// Type for table meta that includes translationPrefix
interface TableMetaWithPrefix {
  translationPrefix?: string;
  [key: string]: unknown;
}

export const getColumnType = <TData>(
  field: string,
  table: Table<TData>,
): string => {
  // First check if we have a manually defined column type
  const meta = table.options.meta as TableMetaWithPrefix | undefined;
  const translationPrefix = meta?.translationPrefix;

  // Check for manually defined column type
  if (translationPrefix && tableFilterColumns[translationPrefix]) {
    const manualColumn = tableFilterColumns[translationPrefix].find(
      (col) => col.id === field,
    );
    if (manualColumn) {
      return manualColumn.filterType;
    }
  }

  // Check if the field name contains specific keywords to determine type
  if (field.toLowerCase().includes("date")) {
    return "date";
  } else if (
    field.toLowerCase().includes("total") ||
    field.toLowerCase().includes("hours") ||
    field.toLowerCase().includes("salary") ||
    field.toLowerCase().includes("duration") ||
    field.toLowerCase().includes("amount") ||
    field.toLowerCase().includes("count")
  ) {
    return "number";
  }

  // Check if the column exists in the table's getAllColumns() result
  // This avoids directly calling getColumn() which throws an error
  const allColumns = table.getAllColumns();
  const matchingColumn = allColumns.find((col) => col.id === field);

  if (matchingColumn) {
    // The column exists, try to get its type from meta
    // We need to use a type assertion here because the Column type from tanstack/react-table
    // doesn't expose columnDef.meta in its type definition
    const columnDef = (
      matchingColumn as unknown as {
        columnDef?: { meta?: { filterType?: string } };
      }
    )?.columnDef;
    if (columnDef?.meta?.filterType) {
      return columnDef.meta.filterType;
    }
  }

  // Default to text type if no type is found
  return "text";
};

export const getFilterableColumns = <TData>(
  table: Table<TData>,
  overridePrefix?: string,
) => {
  // Use the override prefix if provided, otherwise get from table meta
  const meta = table.options.meta as TableMetaWithPrefix | undefined;
  const translationPrefix = overridePrefix || meta?.translationPrefix;

  if (translationPrefix && tableFilterColumns[translationPrefix]) {
    return tableFilterColumns[translationPrefix].map((col) => ({
      id: col.id,
      getCanFilter: () => true,
    }));
  }

  // Fallback to dynamic columns from the table
  return table
    .getAllColumns()
    .filter(
      (column) =>
        column.getCanFilter() &&
        column.id !== "select" &&
        column.id !== "actions" &&
        column.id !== "number" &&
        column.id !== "id",
    );
};

export const getOperators = (columnType: string, t: TFunction) => {
  switch (columnType) {
    case "date":
      return [
        { value: "before", label: t("common.Table.filter-operators.before") },
        { value: "after", label: t("common.Table.filter-operators.after") },
        { value: "equals", label: t("common.Table.filter-operators.equals") },
      ];
    case "number":
      return [
        { value: "equals", label: t("common.Table.filter-operators.equals") },
        { value: "greater", label: t("common.Table.filter-operators.greater") },
        { value: "less", label: t("common.Table.filter-operators.less") },
        { value: "between", label: t("common.Table.filter-operators.between") },
      ];
    default:
      return [
        {
          value: "contains",
          label: t("common.Table.filter-operators.contains"),
        },
        { value: "equals", label: t("common.Table.filter-operators.equals") },
        { value: "starts", label: t("common.Table.filter-operators.starts") },
        { value: "ends", label: t("common.Table.filter-operators.ends") },
      ];
  }
};

export const evaluateRule = <TData extends Record<string, unknown>>(
  rule: FilterRule,
  row: TData,
): boolean => {
  const value = row[rule.field];
  if (!value) return false;

  const ruleValue = rule.value;

  switch (rule.operator) {
    case "contains":
      return String(value)
        .toLowerCase()
        .includes(String(ruleValue).toLowerCase());
    case "equals":
      return typeof value === "number"
        ? Number(value) === Number(ruleValue)
        : String(value).toLowerCase() === String(ruleValue).toLowerCase();
    case "starts":
      return String(value)
        .toLowerCase()
        .startsWith(String(ruleValue).toLowerCase());
    case "ends":
      return String(value)
        .toLowerCase()
        .endsWith(String(ruleValue).toLowerCase());
    case "greater":
      return Number(value) > Number(ruleValue);
    case "less":
      return Number(value) < Number(ruleValue);
    case "before":
      return new Date(String(value)) < new Date(String(ruleValue));
    case "after":
      return new Date(String(value)) > new Date(String(ruleValue));
    default:
      return false;
  }
};

export const applyFiltersToData = <TData extends Record<string, unknown>>(
  data: TData[],
  groups: TFilterGroup[],
): TData[] => {
  if (groups.length === 0 || !groups[0] || groups[0].rules.length === 0)
    return data;

  // Get valid rules from the first group (we only have one group now)
  const validRules = groups[0].rules.filter(
    (rule) => rule.field && rule.operator && rule.value,
  );

  if (validRules.length === 0) return data;

  return data.filter((row) => {
    // Check if all rules are satisfied (AND between rules)
    return validRules.every((rule) => evaluateRule(rule, row));
  });
};
