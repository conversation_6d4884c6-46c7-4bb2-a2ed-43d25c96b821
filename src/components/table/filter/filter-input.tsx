import { DatePicker } from "@/components/ui/date-picker";
import { Input } from "@/components/ui/input";
import { TFunction } from "@/types";
import { FilterRule, ColumnWithMeta } from "../types";

type FilterInputProps<TData> = {
  rule: FilterRule;
  column: ColumnWithMeta<TData> | undefined;
  updateRule: (
    groupId: string,
    ruleId: string,
    field: keyof FilterRule,
    value: string | Date,
  ) => void;
  t: TFunction;
  groupId: string;
};

export function FilterInput<TData>({
  rule,
  column,
  updateRule,
  t,
  groupId,
}: FilterInputProps<TData>) {
  // Get the column meta data
  const meta = column?.columnDef?.meta;

  // Determine the column type based on field name or meta
  let columnType = "text";

  // Check if field name contains date
  if (rule.field.toLowerCase().includes("date")) {
    columnType = "date";
  }
  // Check if field name contains number-related terms
  else if (
    rule.field.toLowerCase().includes("total") ||
    rule.field.toLowerCase().includes("hours") ||
    rule.field.toLowerCase().includes("salary") ||
    rule.field.toLowerCase().includes("duration") ||
    rule.field.toLowerCase().includes("amount")
  ) {
    columnType = "number";
  }

  // If we have meta data, use that type
  if (meta?.filterType) {
    columnType = meta.filterType;
  }

  // Use the determined column type
  if (columnType === "date") {
    return (
      <DatePicker
        date={rule.value instanceof Date ? rule.value : undefined}
        setDate={(date) => updateRule(groupId, rule.id, "value", date || "")}
        placeholder={t("common.Table.filter-options.selectDate")}
        className="w-full max-md:order-4 max-md:col-span-2 h-10 bg-white border-gray-200 rounded-md font-normal text-sm"
      />
    );
  }

  // For number type, use number input
  if (columnType === "number") {
    return (
      <Input
        type="number"
        value={typeof rule.value === "string" ? rule.value : ""}
        onChange={(e) => updateRule(groupId, rule.id, "value", e.target.value)}
        placeholder={t("common.Table.filter-options.enterValue")}
        className="w-full max-md:order-4 max-md:col-span-2 h-10 bg-white border-gray-200 rounded-md font-normal text-sm"
      />
    );
  }

  // For other types, use regular text input
  return (
    <Input
      value={typeof rule.value === "string" ? rule.value : ""}
      onChange={(e) => updateRule(groupId, rule.id, "value", e.target.value)}
      placeholder={t("common.Table.filter-options.enterValue")}
      className="w-full max-md:order-4 max-md:col-span-2 h-10 bg-white border-gray-200 rounded-md font-normal text-sm mt-0"
    />
  );
}
