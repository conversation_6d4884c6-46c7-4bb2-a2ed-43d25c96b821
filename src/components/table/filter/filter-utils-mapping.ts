// Helper function to determine if a field is likely a date field
export const isDateField = (fieldName?: string) => {
  if (!fieldName) return false;
  return (
    fieldName.toLowerCase().includes("date") ||
    fieldName.toLowerCase().includes("time") ||
    fieldName.toLowerCase().includes("day") ||
    fieldName.toLowerCase().includes("month") ||
    fieldName.toLowerCase().includes("year")
  );
};

// Mapping functions for operators and predicates
export function mapOpToPred(op: string) {
  switch (op) {
    case "equals":
      return "eq";
    case "contains":
      return "cont";
    case "starts":
      return "start";
    case "ends":
      return "end";
    case "greater":
      return "gt";
    case "after":
      return "gt";
    case "less":
      return "lt";
    case "before":
      return "lt";
    case "between":
      return ["gteq", "lteq"];
    default:
      return op; // fallback
  }
}

export function mapPredToOp(pred: string, field?: string) {
  // Use the shared isDateField function
  switch (pred) {
    case "eq":
      return "equals";
    case "cont":
      return "contains";
    case "gt": {
      // Use the field name to determine if this is a date field
      return isDateField(field) ? "after" : "greater";
    }
    case "lt": {
      // Use the field name to determine if this is a date field
      return isDateField(field) ? "before" : "less";
    }
    case "start":
      return "starts";
    case "end":
      return "ends";
    case "gteq":
      return "greater"; // For between ranges
    case "lteq":
      return "less"; // For between ranges
    default:
      return pred;
  }
}
