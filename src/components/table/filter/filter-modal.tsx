"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { ResponsivePopover } from "../../responsive-popover";
import { X } from "lucide-react";
import { useTranslations } from "next-intl";
import { useEffect } from "react";
import { FilterModalProps } from "../types";
import { FilterRuleList } from "./filter-rule-list";
import { useTableFilter } from "@/hooks/use-table-filter";
import { SliderSettings } from "../../../../public/images/icons";
import { useRouter, useSearchParams, usePathname } from "next/navigation";
import {
  parseFiltersFromUrl,
  createUrlParamsFromFilters,
} from "./filter-url-parser";

export function FilterModal<TData>({
  open,
  onOpenChange,
  children,
  table,
  translationPrefix,
}: FilterModalProps<TData>) {
  const t = useTranslations();
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();

  const {
    groups,
    setGroups,
    ruleRefs,
    lastAddedRuleId,
    setLastAddedRuleId,
    addRule,
    removeRule,
    handleFilterChange,
    clearFilters,
  } = useTableFilter();

  // Clean up refs when rules change
  useEffect(() => {
    Object.keys(ruleRefs.current).forEach((ruleId) => {
      if (
        !groups.some((group) => group.rules.some((rule) => rule.id === ruleId))
      ) {
        delete ruleRefs.current[ruleId];
      }
    });

    if (lastAddedRuleId && ruleRefs.current[lastAddedRuleId]) {
      ruleRefs.current[lastAddedRuleId]?.scrollIntoView({
        behavior: "smooth",
        block: "nearest",
      });
      setLastAddedRuleId(null);
    }
  }, [lastAddedRuleId, groups, ruleRefs, setLastAddedRuleId]);

  // Parse filter data from URL and create filter groups
  useEffect(() => {
    const filters = searchParams.get("filters");
    if (!filters || filters.length === 0) return;

    const newGroups = parseFiltersFromUrl(filters);
    if (newGroups.length > 0) {
      setGroups(newGroups);
    }
  }, [searchParams, setGroups]);

  // Check if all groups have valid rules (with field, operator, and value)
  const hasValidFilters = () => {
    // Check if there are any groups
    if (groups.length === 0) return false;

    // Check if at least one group has valid rules
    return groups.some((group) => {
      // Check if the group has any rules
      if (group.rules.length === 0) return false;

      // Check if at least one rule in the group is valid
      return group.rules.some(
        (rule) => rule.field && rule.operator && rule.value,
      );
    });
  };

  const applyFilters = () => {
    if (hasValidFilters()) {
      const newParams = createUrlParamsFromFilters(groups, searchParams);
      // Update URL
      router.replace(`${pathname}?${newParams.toString()}`);
    }

    // Close the modal
    onOpenChange(false);
  };

  // Handle key press events for the modal
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      e.preventDefault();
      applyFilters();
    }
  };

  // Create header content
  const headerContent = (
    <div className="flex items-center justify-between rounded-xl">
      <h2 className="text-lg sm:text-lg font-semibold leading-7 flex items-center gap-2">
        <span>
          <SliderSettings className="mr-2 scale-125" />
        </span>
        <span>{t("common.Table.filter")}</span>
      </h2>
      <Button
        variant="outline"
        className="border-gray-300 w-3 h-7 px-3.5 text-gray-700 rounded-lg opacity-70 transition-opacity hover:opacity-100 disabled:pointer-events-none"
        onClick={() => onOpenChange(false)}
      >
        <X className="!h-6 !w-6 stroke-[1.3px]" />
        <span className="sr-only">Close</span>
      </Button>
    </div>
  );

  // Create footer content
  const footerContent = (
    <div className="rounded-[20px] flex flex-row items-center justify-between gap-4 sm:gap-6">
      <Button
        type="button"
        onClick={() => applyFilters()}
        className="px-4 py-2 w-full h-12 sm:max-w-[244px] rounded-lg text-sm md:text-base"
        disabled={!hasValidFilters()}
      >
        {t("common.Table.apply").split(" ")[0]}
      </Button>
      <Button
        variant="outline"
        type="button"
        className="w-full h-12 sm:max-w-[244px] rounded-lg text-sm md:text-base"
        onClick={() => clearFilters()}
      >
        {t("common.Table.clear")}
      </Button>
    </div>
  );

  return (
    <ResponsivePopover
      trigger={children}
      open={open}
      onOpenChange={onOpenChange}
      align="end"
      sideOffset={5}
      className="shadow-lg w-full max-w-[561px] md:w-[561px] rounded-[20px]"
      header={headerContent}
      footer={footerContent}
      headerClassName="px-6 py-5 border-b bg-white sticky top-0 z-10 rounded-[20px]"
      contentClassName="max-h-[40vh]"
    >
      <div
        className="p-6 space-y-6 max-w-[561px]"
        onKeyDown={handleKeyDown}
        tabIndex={0}
      >
        {groups.length > 0 ? (
          groups.map((group) => (
            <div key={group.id} className="space-y-6">
              <FilterRuleList
                key={group.id}
                group={group}
                table={table}
                onUpdateRule={handleFilterChange}
                onAddRule={() => addRule(group.id)}
                onRemoveRule={(ruleId) => removeRule(group.id, ruleId)}
                translationPrefix={translationPrefix}
                t={t}
                ruleRefs={ruleRefs}
              />
            </div>
          ))
        ) : (
          <div className="min-h-[100px] border border-dashed border-gray-200 rounded-md flex items-center justify-center mb-6">
            <p className="text-gray-400 text-sm">
              {t("common.Table.filter-options.title")}
            </p>
          </div>
        )}
      </div>
    </ResponsivePopover>
  );
}
