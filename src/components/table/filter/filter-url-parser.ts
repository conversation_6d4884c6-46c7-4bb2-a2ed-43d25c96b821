import { TFilterGroup } from "../types";
import { isDateField, mapPredToOp } from "./filter-utils-mapping";

export const extractFilterParts = (key: string) => {
  const match = key.match(/filter\[([^_]+)_([^\]]+)\]/);
  return match && match.length === 3
    ? { field: match[1], predicate: match[2] }
    : null;
};

export const parseFiltersFromUrl = (filtersParam: string): TFilterGroup[] => {
  if (!filtersParam || filtersParam.length === 0) return [];

  try {
    const parsedFilters = new URLSearchParams(filtersParam);
    const timestamp = Date.now();

    // Collect all rules
    const rules: Array<{ field: string; predicate: string; value: string }> =
      [];

    // Collect rules from URL parameters
    parsedFilters.forEach((value, key) => {
      if (key.startsWith("filter[")) {
        const parts = extractFilterParts(key);
        if (parts) {
          const { field, predicate } = parts;

          // Add rule to the list
          rules.push({
            field,
            predicate,
            value,
          });
        }
      }
    });

    // Create a single filter group with all rules
    if (rules.length > 0) {
      return [
        {
          id: `group-${timestamp}`,
          rules: rules.map((rule, ruleIdx) => ({
            id: `rule-${timestamp}-${ruleIdx}`,
            field: rule.field,
            operator: mapPredToOp(rule.predicate, rule.field),
            value: isDateField(rule.field) ? new Date(rule.value) : rule.value,
          })),
        },
      ];
    }

    return [];
  } catch (error) {
    // Silent error handling for filter parsing issues
    return [];
  }
};

export const createUrlParamsFromFilters = (
  groups: TFilterGroup[],
  currentParams: URLSearchParams,
) => {
  const newParams = new URLSearchParams();

  // Copy over only non-filter related parameters from the current URL
  currentParams.forEach((value, key) => {
    // Skip any filter-related parameters
    if (key !== "filters") {
      newParams.append(key, value);
    }
  });

  const validGroups = groups.filter((group) => group.rules.length > 0);

  const hasCompleteRules = validGroups.some((group) =>
    group.rules.some((rule) => rule.field && rule.operator && rule.value),
  );

  if (validGroups.length > 0 && hasCompleteRules) {
    const filterParams = new URLSearchParams();

    // Process valid groups and their rules
    validGroups.forEach((group) => {
      // Get rules that have all required fields
      const validRules = group.rules.filter(
        (rule) => rule.field && rule.operator && rule.value,
      );

      // Only process groups that have at least one valid rule
      if (validRules.length > 0) {
        // Process each valid rule in the group
        validRules.forEach((rule) => {
          const pred =
            rule.operator === "between"
              ? ["gteq", "lteq"]
              : rule.operator === "after" || rule.operator === "greater"
              ? "gt"
              : rule.operator === "before" || rule.operator === "less"
              ? "lt"
              : rule.operator === "contains"
              ? "cont"
              : rule.operator === "equals"
              ? "eq"
              : rule.operator === "starts"
              ? "start"
              : rule.operator === "ends"
              ? "end"
              : rule.operator;

          if (Array.isArray(pred)) {
            // Handle between range (not implemented in this example)
            return;
          }

          const filterKey = `filter[${rule.field}_${pred}]`;

          // Format date values properly for the API
          const valueToSend =
            rule.value instanceof Date
              ? rule.value.toISOString()
              : rule.value.toString();

          // Add the filter rule
          filterParams.append(filterKey, valueToSend);
        });
      }
    });

    // Only update URL if we have valid filters
    if (filterParams.toString()) {
      newParams.set("filters", filterParams.toString());
    }
  }

  return newParams;
};
