import { Table, Column } from "@tanstack/react-table";

export type CustomColumnMeta = {
  filterType?: "text" | "date" | "number";
};

// type for manually defined filterable column
export type FilterableColumn = {
  id: string;
  filterType: "text" | "date" | "number";
};

export type FilterModalProps<TData> = {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  children: React.ReactNode;
  table: Table<TData>;
  translationPrefix: string;
};

export type FilterRule = {
  id: string;
  field: string;
  operator: string;
  value: string | Date;
};

export type TFilterGroup = {
  id: string;
  rules: FilterRule[];
};

// Extend the Column type with our custom meta
export type ColumnWithMeta<TData> = Column<TData> & {
  columnDef: {
    meta?: CustomColumnMeta;
  };
};
