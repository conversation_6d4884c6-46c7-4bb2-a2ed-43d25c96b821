"use client";

import React, { Component, ErrorInfo, ReactNode } from "react";
import ErrorMessage from "./errorMessage";

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
  error: Error | null;
}

class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
    };
  }

  static getDerivedStateFromError(error: Error): State {
    return {
      hasError: true,
      error,
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo): void {
    // Only log errors if not in production or if NEXT_SUPPRESS_ERRORS is not set to true
    if (process.env.NODE_ENV !== 'production' || process.env.NEXT_SUPPRESS_ERRORS !== 'true') {
      console.error("Error caught by ErrorBoundary:", error, errorInfo);
    }
  }

  render(): ReactNode {
    if (this.state.hasError) {
      // If a fallback is provided, use it
      if (this.props.fallback) {
        return this.props.fallback;
      }

      // In production, show a minimal error message
      if (process.env.NODE_ENV === 'production' || process.env.NEXT_SUPPRESS_ERRORS === 'true') {
        return (
          <div className="flex flex-col items-center justify-center min-h-[200px] bg-[hsl(var(--background-v2))] px-6">
            <div className="bg-[hsl(var(--card))] shadow-lg rounded-[var(--radius)] p-4 max-w-md text-center border border-[hsl(var(--border))]">
              <h2 className="text-xl font-bold text-[hsl(var(--error))]">
                An error occurred
              </h2>
              <button
                className="mt-2 px-3 py-1 bg-[hsl(var(--secondary))] text-[hsl(var(--primary-foreground))] font-medium rounded-md hover:brightness-90 transition-all"
                onClick={() => this.setState({ hasError: false, error: null })}
                type="button"
              >
                Try again
              </button>
            </div>
          </div>
        );
      }

      // In development, show a more detailed error message
      return (
        <div className="p-4 border border-red-300 rounded-md bg-red-50">
          <h2 className="text-lg font-semibold text-red-800">An error occurred</h2>
          {this.state.error && (
            <ErrorMessage message={this.state.error.message} />
          )}
          <button
            className="mt-2 px-3 py-1 bg-red-100 text-red-800 font-medium rounded-md hover:bg-red-200 transition-all"
            onClick={() => this.setState({ hasError: false, error: null })}
            type="button"
          >
            Try again
          </button>
        </div>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;