"use client";

import * as React from "react";
import { CalendarIcon } from "lucide-react";
import { useLocale } from "next-intl";
// Import locales for date-fns
import { ar, enUS } from "date-fns/locale";

import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { LANGUAGES } from "@/constants/enum";
import { formatDate } from "@/lib/dateFormatter";

interface DatePickerProps {
  date: Date | undefined;
  setDate: (date: Date | undefined) => void;
  placeholder?: string;
  className?: string;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  disabled?: boolean;
}

export function DatePicker({
  date,
  setDate,
  placeholder,
  className,
  open,
  onOpenChange,
  disabled,
}: DatePickerProps) {
  const locale = useLocale();
  const isArabic = locale === LANGUAGES.ARABIC;
  const formatedDate = formatDate(date ?? null, locale ?? "en");
  // Internal state for uncontrolled usage
  const [internalOpen, setInternalOpen] = React.useState(false);

  // Determine if we're in controlled or uncontrolled mode
  const isControlled = open !== undefined;
  const isOpen = isControlled ? open : internalOpen;

  // Handle open state changes
  const handleOpenChange = React.useCallback(
    (newOpen: boolean) => {
      if (!isControlled) {
        setInternalOpen(newOpen);
      }
      onOpenChange?.(newOpen);
    },
    [isControlled, onOpenChange],
  );

  return (
    <Popover modal={true} open={isOpen} onOpenChange={handleOpenChange}>
      <PopoverTrigger asChild>
        <Button
          variant={"outline"}
          disabled={disabled}
          className={cn(
            "w-full justify-start text-left font-normal h-10 rounded-md border border-input bg-transparent px-3 py-1 text-base transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm shadow",
            !date && "text-muted-foreground z-50",
            className,
          )}
        >
          <CalendarIcon className="ms-0.5 !h-5 !w-5 text-gray-500" />
          {date ? (
            formatedDate
          ) : (
            <span>
              {placeholder || (isArabic ? "اختر تاريخًا" : "Pick a date")}
            </span>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent
        lang={locale}
        dir={isArabic ? "rtl" : "ltr"}
        className="w-auto p-0"
        align="start"
        side="bottom"
      >
        <Calendar
          mode="single"
          selected={date}
          onSelect={setDate}
          initialFocus
          lang={locale}
          locale={locale === LANGUAGES.ARABIC ? ar : enUS}
          customStyles={{
            dayWidth: "w-10",
          }}
        />
      </PopoverContent>
    </Popover>
  );
}
