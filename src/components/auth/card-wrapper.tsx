"use client";

import { Card, CardContent, CardHeader } from "../ui/card";
import Header from "./header";

type CardWrapperProps = {
  children: React.ReactNode;
  headerTitle: string;
  headerLabel: string;
};
const CardWrapper = ({
  children,
  headerTitle,
  headerLabel,
}: CardWrapperProps) => {
  return (
    <Card className="container w-full max-w-[520px] lg:w-[520px] rounded-2xl p-4 sm:p-6 space-y-10 shadow-none border border-border">
      <CardHeader className="!p-0 m-0">
        <Header title={headerTitle} label={headerLabel} />
      </CardHeader>
      <CardContent className="!p-0 m-0">{children}</CardContent>
    </Card>
  );
};

export default CardWrapper;
