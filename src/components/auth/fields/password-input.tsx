import React, { useState } from "react";
import { Input } from "@/components/ui/input";
import { Eye, EyeOffIcon } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Lock } from "../../../../public/images/icons";
import { cn } from "@/lib/utils";
import type { ControllerRenderProps, FieldValues, Path } from "react-hook-form";

type PasswordInputProps<T extends FieldValues> = {
  field: ControllerRenderProps<T, Path<T>>;
  placeholder?: string;
  className?: string;
  isPending?: boolean;
  disableToggle?: boolean;
  readOnly?: boolean;
  onClick?: () => void;
};

export default function PasswordInput<T extends FieldValues>({
  field,
  placeholder,
  className,
  isPending,
  readOnly = false,
  disableToggle = false,
  onClick,
}: PasswordInputProps<T>) {
  const [isVisible, setIsVisible] = useState(false);

  const inputType = isVisible ? "text" : "password";

  return (
    <div className="relative !m-0">
      <Input
        autoComplete="current-password"
        type={inputType}
        placeholder={placeholder}
        {...field}
        className={cn(
          `px-10 placeholder:tracking-[2px] placeholder:text-3xl placeholder:absolute placeholder:top-0 placeholder:start-10 ${className}`,
          { "!text-2xl tracking-wider": !isVisible }
        )}
        disabled={isPending}
        readOnly={readOnly}
        onClick={onClick}
      />
      {/* Only render the toggle button if disableToggle is false */}
      {(!disableToggle || readOnly !== false) && (
        <Button
          variant={null}
          type="button"
          className="absolute end-3 top-1/2 transform -translate-y-1/2 text-gray-500 p-0"
          onClick={() => setIsVisible(!isVisible)}
          aria-label={!isVisible ? "Show password" : "Hide password"}
        >
          {!isVisible ? (
            <EyeOffIcon className="!w-5 !h-5" />
          ) : (
            <Eye className="!w-5 !h-5" />
          )}
        </Button>
      )}
      <Lock className="absolute start-3.5 top-1/2 transform -translate-y-1/2 text-gray-500" />
    </div>
  );
}
