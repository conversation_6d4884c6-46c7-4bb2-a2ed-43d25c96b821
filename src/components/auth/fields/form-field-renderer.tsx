import {
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import PasswordInput from "./password-input";
import { TChangePasswordFormProps, TinputField } from "@/types";
import { FieldValues, Path, UseFormReturn } from "react-hook-form";
import EmailInput from "./email-input";
import PhoneInput from "./phone-input";
import { cn } from "@/lib/utils";
import React from "react";
import { iconMap } from "@/constants";
import ProfileImageInput from "./profile-image-input";
import FileAttachmentsInput from "./file-attachments-input/index";
import AssignmentsInput from "./assignments-input";
import { Textarea } from "@/components/ui/textarea";
import { formatNumber } from "@/lib/format-number";
import { useLocale } from "next-intl";
import { Locale } from "@/i18n/routing";
import { ControlledSelect } from "@/components/ui/controlled-select";
import { DatePicker } from "@/components/ui/date-picker";

type FormFieldRendererProps<T extends FieldValues> = {
  fieldConfig: TinputField<T>;
  form: UseFormReturn<T>;
  isPending?: boolean;
  selectOpenState?: { [key: string]: boolean };
  onSelectOpenChange?: (name: string, open: boolean) => void;
  datePickerOpenState?: { [key: string]: boolean };
  onDatePickerOpenChange?: (name: string, open: boolean) => void;
} & TChangePasswordFormProps;

const FormFieldRenderer = <T extends FieldValues>({
  fieldConfig,
  form,
  isPending,
  onPasswordChanged,
  selectOpenState,
  onSelectOpenChange,
  datePickerOpenState,
  onDatePickerOpenChange,
}: FormFieldRendererProps<T>) => {
  const locale: Locale = useLocale() as Locale;
  return (
    <FormField
      control={form.control}
      name={fieldConfig.name as Path<T>}
      render={({ field }) => {
        const error = form.formState.errors[fieldConfig.name!];
        const errorStyle = "focus-visible:!ring-red-500";
        const labelClassName = fieldConfig.labelClassName;

        // Helper function to format numeric values
        const formatFieldValue = (value: string) => {
          if (!value) return;

          const numericValue = value.replace(/,/g, "");
          if (!isNaN(Number(numericValue))) {
            const formattedValue = formatNumber(Number(numericValue), locale);
            form.setValue(
              fieldConfig.name as Path<T>,
              formattedValue as unknown as T[keyof T],
              {
                shouldValidate: true,
              },
            );
          }
        };

        const handleBlur = () => {
          if (fieldConfig.formatOnBlur) {
            // Ensure we're working with a string value
            const value = field.value != null ? String(field.value) : "";
            console.log(
              "handleBlur value:",
              value,
              "type:",
              typeof field.value,
            );
            formatFieldValue(value);
          }
        };
        const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
          // First update the field value with the raw input
          field.onChange(e);

          // Then format if formatOnChange is enabled
          if (fieldConfig.formatOnChange) {
            formatFieldValue(e.target.value);
          }
        };

        return (
          <FormItem>
            <FormLabel
              className={cn(
                "capitalize text-gray-500 font-medium text-sm leading-5",
                labelClassName,
              )}
            >
              {fieldConfig.label}
            </FormLabel>
            <FormControl>
              {fieldConfig.type === "password" ? (
                <PasswordInput
                  field={field}
                  placeholder={fieldConfig.placeholder}
                  className={cn(error && errorStyle, fieldConfig.className)}
                  isPending={isPending}
                  disableToggle={fieldConfig.disableToggle}
                  readOnly={fieldConfig.readOnly}
                  onClick={
                    fieldConfig.readOnly && onPasswordChanged
                      ? () => onPasswordChanged()
                      : undefined
                  }
                />
              ) : fieldConfig.type === "email" ? (
                <EmailInput
                  field={field}
                  placeholder={fieldConfig.placeholder}
                  className={cn(error && errorStyle, fieldConfig.className)}
                  isPending={isPending}
                  readOnly={fieldConfig.readOnly}
                />
              ) : fieldConfig.type === "tel" ? (
                <PhoneInput
                  field={field}
                  placeholder={fieldConfig.placeholder}
                  className={cn(error && errorStyle, fieldConfig.className)}
                  isPending={isPending}
                  readOnly={fieldConfig.readOnly}
                />
              ) : fieldConfig.type === "file" ? (
                <ProfileImageInput
                  field={field}
                  isPending={isPending}
                  readOnly={fieldConfig.readOnly}
                  defaultImage={fieldConfig.defaultImage}
                  fallbackText={fieldConfig.fallbackText}
                  fallbackInitials={fieldConfig.fallbackInitials}
                  avatarSize={
                    fieldConfig.avatarSize as "sm" | "md" | "lg" | "xl"
                  }
                  containerClassName={fieldConfig.containerClassName}
                  buttonsClassName={fieldConfig.buttonsClassName}
                  addBtnClassName={fieldConfig.addBtnClassName}
                  deleteBtnClassName={fieldConfig.deleteBtnClassName}
                  deleteButtonText={fieldConfig.deleteButtonText}
                  changeButtonText={fieldConfig.changeButtonText}
                  onImageChange={fieldConfig.onImageChange}
                  deleteTranslationKey={fieldConfig.deleteTranslationKey}
                  changeTranslationKey={fieldConfig.changeTranslationKey}
                  changeButtonIcon={fieldConfig.changeButtonIcon}
                />
              ) : fieldConfig.type === "attachments" ? (
                <FileAttachmentsInput
                  field={field as unknown as any}
                  isPending={isPending}
                  readOnly={fieldConfig.readOnly}
                  accept={fieldConfig.accept}
                  multiple={fieldConfig.multiple}
                  maxFiles={fieldConfig.maxFiles}
                  maxSize={fieldConfig.maxSize}
                  containerClassName={fieldConfig.containerClassName}
                  inputClassName={fieldConfig.inputClassName}
                  fileListClassName={fieldConfig.fileListClassName}
                  buttonClassName={fieldConfig.buttonClassName}
                  uploadButtonText={fieldConfig.uploadButtonText}
                  uploadTranslationKey={fieldConfig.uploadTranslationKey}
                  onFilesChange={fieldConfig.onFilesChange}
                  uploadButtonIcon={fieldConfig.uploadButtonIcon}
                  enableDragDrop={fieldConfig.enableDragDrop}
                />
              ) : fieldConfig.type === "assignments" ? (
                <AssignmentsInput
                  name={field.name}
                  label={fieldConfig.label}
                  isPending={isPending}
                  readOnly={fieldConfig.readOnly}
                  containerClassName={fieldConfig.containerClassName}
                  labelClassName={fieldConfig.labelClassName}
                />
              ) : fieldConfig.type === "textarea" ? (
                <Textarea
                  {...field}
                  placeholder={fieldConfig.placeholder}
                  className={cn(
                    "w-full text-sm md:text-base rounded-md border p-3 resize-none",
                    error && errorStyle,
                    fieldConfig.className,
                  )}
                  disabled={isPending}
                />
              ) : fieldConfig.type === "date" ? (
                <DatePicker
                  date={field.value as Date | undefined}
                  setDate={(date: Date | undefined) => {
                    field.onChange(date);
                    field.onBlur(); // Trigger validation

                    // Force a re-render to ensure the form state is updated
                    setTimeout(() => {
                      form.trigger(field.name);
                    }, 0);
                  }}
                  placeholder={fieldConfig.placeholder}
                  className={cn(fieldConfig.className, error && errorStyle)}
                  disabled={isPending || fieldConfig.readOnly}
                  open={datePickerOpenState?.[field.name]}
                  onOpenChange={(open) =>
                    onDatePickerOpenChange?.(field.name, open)
                  }
                />
              ) : fieldConfig.type === "select" ? (
                <ControlledSelect
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                  disabled={isPending || fieldConfig.readOnly}
                  placeholder={fieldConfig.placeholder}
                  triggerClassName={cn(
                    error && errorStyle,
                    fieldConfig.className,
                  )}
                  dir={locale}
                  options={fieldConfig.options}
                  open={selectOpenState?.[field.name]}
                  onOpenChange={(open) =>
                    onSelectOpenChange?.(field.name, open)
                  }
                />
              ) : (
                <div className="relative">
                  {fieldConfig.leftIcon && (
                    <div className="absolute left-3 top-1/2 transform -translate-y-1/2 pointer-events-none">
                      {React.createElement(fieldConfig.leftIcon, {
                        className: "fill-current",
                      })}
                    </div>
                  )}
                  <Input
                    placeholder={fieldConfig.placeholder}
                    {...field}
                    className={cn(
                      "text-sm md:text-base",
                      error && errorStyle,
                      fieldConfig.leftIcon ? "pe-10" : "pe-3",
                      fieldConfig.rightIcon ? "ps-10" : "ps-3",
                      fieldConfig.className,
                    )}
                    disabled={isPending}
                    onChange={handleChange}
                    onBlur={() => {
                      field.onBlur(); // Trigger React Hook Form's blur handling
                      handleBlur(); // Format the value
                    }}
                  />
                  {fieldConfig.rightIcon && (
                    <div className="absolute start-3 top-1/2 transform -translate-y-1/2">
                      {React.createElement(iconMap[fieldConfig.rightIcon], {
                        className: "stroke-gray-500",
                      })}
                    </div>
                  )}
                </div>
              )}
            </FormControl>
            <FormMessage className="font-semibold" />
          </FormItem>
        );
      }}
    />
  );
};

export default FormFieldRenderer;
