"use client";

import React, { useEffect, useRef, useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { useTranslations } from "next-intl";
import { useUser } from "@/contexts/user-provider";
import { cn } from "@/lib/utils";
import { Plus } from "lucide-react";
import { TUser } from "@/types/auth";

type ProfileImageInputProps = {
  field: {
    value: File | null | string;
    onChange: (file: File | null | string) => void;
  };
  label?: string;
  isPending?: boolean;
  readOnly?: boolean;
  // New props for more flexibility
  defaultImage?: string;
  fallbackText?: string;
  fallbackInitials?: string;
  avatarSize?: "sm" | "md" | "lg" | "xl";
  containerClassName?: string;
  buttonsClassName?: string;
  deleteButtonText?: string;
  changeButtonText?: string;
  addBtnClassName?: string;
  deleteBtnClassName?: string;
  // Optional callback when image changes
  onImageChange?: (file: File | null | string) => void;
  // Translation keys for buttons
  deleteTranslationKey?: string;
  changeTranslationKey?: string;
  // Icon for the change button
  changeButtonIcon?: React.ReactNode;
};

export default function ProfileImageInput({
  field,
  isPending,
  readOnly,
  defaultImage,
  fallbackText,
  fallbackInitials,
  avatarSize = "md",
  containerClassName,
  buttonsClassName,
  addBtnClassName,
  deleteBtnClassName,
  deleteButtonText,
  changeButtonText,
  onImageChange,
  deleteTranslationKey = "common.form.profile-image.buttons.delete",
  changeTranslationKey = "common.form.profile-image.buttons.change",
  changeButtonIcon,
}: ProfileImageInputProps) {
  const hiddenInputRef = useRef<HTMLInputElement | null>(null);
  const t = useTranslations();
  const { user } = useUser();

  // Use defaultImage if provided, otherwise fall back to user context
  const userProfileImage = user ? (user as TUser).avatar : null;
  // Always ensure we have a valid default image path, falling back to the built-in default
  const defaultImageValue = defaultImage ?? userProfileImage ?? "";

  // Track whether a custom image has been selected
  const [hasCustomImage, setHasCustomImage] = useState<boolean>(false);
  const [preview, setPreview] = useState<string | null>(defaultImageValue);

  // Get avatar size class based on the size prop
  const getAvatarSizeClass = () => {
    switch (avatarSize) {
      case "sm":
        return "w-16 h-16 md:w-16 md:h-16";
      case "md":
        return "w-[120px] h-[120px] md:w-20 md:h-20";
      case "lg":
        return "w-32 h-32 md:w-32 md:h-32";
      case "xl":
        return "w-40 h-40 md:w-40 md:h-40";
      default:
        return "w-[120px] h-[120px] md:w-20 md:h-20";
    }
  };

  // Sync preview with field.value
  useEffect(() => {
    if (field.value instanceof File) {
      // If it's a File object, it's definitely a custom image
      setPreview(URL.createObjectURL(field.value));
      setHasCustomImage(true);
    } else if (field.value === null) {
      // If value is null, use the default image
      setPreview(defaultImageValue);
      setHasCustomImage(false);
    } else {
      // If it's a string URL
      setPreview(field.value);
      setHasCustomImage(false);
    }
  }, [field.value, defaultImageValue]);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) {
      // If no file is selected, keep the default image
      field.onChange(defaultImageValue);
      onImageChange?.(defaultImageValue);
      setHasCustomImage(false);
      return;
    }
    // When a file is selected, update the field with the file
    field.onChange(file);
    onImageChange?.(file);
    setHasCustomImage(true); // Set to true when a custom image is selected
  };

  const handleRemoveImage = () => {
    // Only allow removing if there's a custom image selected
    if (hasCustomImage) {
      // When removing, always revert to the default image (never null)
      field.onChange(defaultImageValue);
      setPreview(defaultImageValue);
      onImageChange?.(defaultImageValue);
      setHasCustomImage(false);

      // Reset the file input value so the same file can be selected again
      if (hiddenInputRef.current) {
        hiddenInputRef.current.value = "";
      }
    }
  };

  // Get the fallback initial for the avatar
  const getFallbackInitial = () => {
    if (fallbackInitials) return fallbackInitials;
    if (fallbackText) return fallbackText[0];
    return user?.name?.[0] || "U";
  };

  return (
    <div
      className={cn(
        "flex flex-col md:flex-row items-center gap-[29px]",
        containerClassName,
      )}
    >
      <div className="relative">
        <Avatar
          className={cn(
            getAvatarSizeClass(),
            "shadow-[0px_0px_0px_2px_inset] shadow-[#EEEDF0]",
          )}
        >
          <AvatarImage
            className="object-cover object-top"
            src={preview || defaultImageValue}
            alt={fallbackText || "User Avatar"}
          />
          <AvatarFallback>{getFallbackInitial()}</AvatarFallback>
        </Avatar>
      </div>

      <div
        className={cn(
          "flex flex-row-reverse items-center gap-3 md:mb-0",
          buttonsClassName,
        )}
      >
        <Button
          variant="outline"
          type="button"
          className={cn("h-full max-h-[37px] text-error", deleteBtnClassName)}
          onClick={handleRemoveImage}
          disabled={isPending || readOnly || !hasCustomImage}
        >
          {deleteButtonText || t(deleteTranslationKey)}
        </Button>

        <Button
          className={cn(
            "h-full max-h-[37px] ltr:max-w-max w-full text-sm font-semibold",
            addBtnClassName,
          )}
          type="button"
          disabled={isPending || readOnly}
          onClick={() => {
            hiddenInputRef.current?.click();
          }}
        >
          {changeButtonText || t(changeTranslationKey)}
          {changeButtonIcon === "plus" ? (
            <span>
              <Plus size={16} />
            </span>
          ) : changeButtonIcon ? (
            <span>{changeButtonIcon}</span>
          ) : null}
        </Button>
      </div>

      {/* Hidden file input */}
      <Input
        type="file"
        id="profileImageInput"
        name="avatar"
        accept="image/*"
        ref={hiddenInputRef}
        onChange={handleFileChange}
        style={{ display: "none" }}
        disabled={isPending || readOnly}
      />
    </div>
  );
}
