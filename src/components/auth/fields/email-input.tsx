import React from "react";
import { Input } from "@/components/ui/input";
import { CircleAlert } from "lucide-react";
import {
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Tooltip } from "@radix-ui/react-tooltip";
import { useTranslations } from "next-intl";
import { cn } from "@/lib/utils";
import { Sms } from "../../../../public/images/icons";
import { ControllerRenderProps, FieldValues, Path } from "react-hook-form";

type EmailInputProps<T extends FieldValues> = {
  field: ControllerRenderProps<T, Path<T>>;
  placeholder?: string;
  className?: string;
  isPending?: boolean;
  readOnly?: boolean;
};

export default function EmailInput<T extends FieldValues>({
  field,
  placeholder,
  className,
  isPending,
  readOnly,
}: EmailInputProps<T>) {
  const t = useTranslations("common");
  return (
    <div className="relative !m-0">
      <Input
        autoComplete="username"
        type={"email"}
        placeholder={placeholder}
        {...field}
        className={cn(`px-10 text-sm md:text-base ${className}`, {
          "pointer-events-none": readOnly === true,
        })}
        disabled={isPending}
        readOnly={readOnly}
      />

      <div
        className={
          "absolute flex end-3 top-1/2 transform -translate-y-1/2 text-gray-500"
        }
      >
        <TooltipProvider delayDuration={200}>
          <Tooltip>
            <TooltipTrigger type="button">
              <CircleAlert className="stroke-2 !w-5 !h-5 group-[.error]:text-red-500:" />
            </TooltipTrigger>
            <TooltipContent className="bg-primary text-secondary-2 rounded-sm p-3 mb-1">
              <p>{t("form.email.tooltip")}</p>
              <div className="border-[10px] absolute left-1/2 -translate-x-1/2 -bottom-3 w-4 h-4 -z-10 border-r-transparent border-b-transparent border-l-transparent border-t-primary "></div>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </div>
      <Sms className="absolute start-3 top-1/2 transform -translate-y-1/2 text-gray-500" />
    </div>
  );
}
