"use client";

import { But<PERSON> } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { useTranslations } from "next-intl";
import { Plus } from "lucide-react";
import { useFieldArray, useFormContext } from "react-hook-form";
import { EmployeeSchemaType } from "@/app/[locale]/_modules/people/schemas/employeeSchema";
import { Label } from "@/components/ui/label";
import { ControlledSelect } from "@/components/ui/controlled-select";
import { useProjects } from "@/hooks/useProjects";
import { useRoles } from "@/hooks/useRoles";
import { Trash } from "../../../../public/images/icons";

type AssignmentsInputProps = {
  name: string;
  label?: string;
  isPending?: boolean;
  readOnly?: boolean;
  containerClassName?: string;
  labelClassName?: string;
};

export default function AssignmentsInput({
  name,
  label,
  isPending,
  readOnly,
  containerClassName,
  labelClassName,
}: AssignmentsInputProps) {
  const t = useTranslations();
  const form = useFormContext<EmployeeSchemaType>();
  const { projects, isLoading: isLoadingProjects } = useProjects();
  const { roles, isLoading: isLoadingRoles } = useRoles();

  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: name as "assignments",
  });

  const handleAddAssignment = () => {
    if (projects && projects.length > 0 && roles && roles.length > 0) {
      append({
        project: projects[0].id.toString(),
        role: roles[0].id.toString(),
      });
    }
  };

  const isLoading = isLoadingProjects || isLoadingRoles;

  return (
    <div className={cn("space-y-4", containerClassName)}>
      {fields.length === 0 ? (
        <div className="text-sm text-gray-500 italic">
          {t("common.form.assignment.empty")}
        </div>
      ) : (
        <div className="space-y-4">
          {fields.map((field, index) => (
            <div
              key={field.id}
              className="py-3 px-2 min-h-[136px] bg-gray-50 rounded-lg border border-gray-100"
            >
              <div className="flex justify-between items-center max-h-5 mb-6">
                <h3 className="text-sm font-medium text-gray-600">
                  {t("common.form.assignment.title")}
                  <span>{` ${index + 1}`}</span>
                </h3>
                <Button
                  type="button"
                  variant="ghost"
                  size="icon"
                  onClick={() => remove(index)}
                  disabled={isPending || readOnly || isLoading}
                  className="!h-auto self-end rounded-full hover:bg-red-50 hover:text-red-500 transition-colors"
                >
                  <Trash className="!h-6 !w-6 text-error" />
                </Button>
              </div>
              <div className="grid grid-cols-[1fr_1fr] gap-3">
                <div>
                  <Label className="mb-2 block text-sm font-medium text-gray-500">
                    {t("common.form.assignment.project.label")}
                  </Label>
                  <ControlledSelect
                    onValueChange={(value) =>
                      form.setValue(
                        `assignments.${index}.project` as const,
                        value,
                      )
                    }
                    value={form.watch(`assignments.${index}.project`)}
                    disabled={isPending || readOnly || isLoading}
                    placeholder={t(
                      "common.form.assignment.project.placeholder",
                    )}
                    triggerClassName="h-10 bg-white border-gray-200 rounded-md font-normal text-xs"
                    options={projects.map((project) => ({
                      value: project.id,
                      label: project.attributes.name,
                    }))}
                  />
                  {form.formState.errors.assignments?.[index]?.project && (
                    <p className="text-xs text-red-500 mt-1">
                      {
                        form.formState.errors.assignments[index]?.project
                          ?.message
                      }
                    </p>
                  )}
                </div>

                <div>
                  <Label className="mb-2 block text-sm text-gray-500">
                    {t("common.form.assignment.role.label")}
                  </Label>
                  <ControlledSelect
                    onValueChange={(value) =>
                      form.setValue(`assignments.${index}.role` as const, value)
                    }
                    value={form.watch(`assignments.${index}.role`)}
                    disabled={isPending || readOnly || isLoading}
                    placeholder={t("common.form.assignment.role.placeholder")}
                    triggerClassName="h-10 bg-white border-gray-200 rounded-md font-normal text-xs"
                    options={roles.map((role) => ({
                      value: role.id,
                      label: role.attributes.name,
                    }))}
                  />
                  {form.formState.errors.assignments?.[index]?.role && (
                    <p className="text-xs text-red-500 mt-1">
                      {form.formState.errors.assignments[index]?.role?.message}
                    </p>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
      <div className="flex justify-between items-center">
        <Button
          type="button"
          variant="ghost"
          size="sm"
          onClick={handleAddAssignment}
          disabled={
            isPending ||
            readOnly ||
            isLoading ||
            projects.length === 0 ||
            roles.length === 0
          }
          className="flex items-center gap-1.5 text-sm font-normal px-3 py-1.5 h-auto rounded-md hover:bg-gray-50 transition-colors"
        >
          <Plus className="h-3.5 w-3.5 text-secondary" />
          <span className="text-secondary">
            {t("common.form.assignment.add")}
          </span>
        </Button>
      </div>
    </div>
  );
}
