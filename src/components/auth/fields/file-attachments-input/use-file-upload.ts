"use client";

import { useState, useEffect } from "react";
import { useToastMessage } from "@/hooks/use-toast-message";
import { useTranslations } from "next-intl";

type UseFileUploadProps = {
  field: {
    value: File[];
    onChange: (files: File[]) => void;
  };
  maxFiles?: number;
  maxSize?: number;
  accept?: string;
  onFilesChange?: (files: File[]) => void;
};

export function useFileUpload({
  field,
  maxFiles = 10,
  maxSize = 5000000, // 5MB default
  accept = "*/*",
  onFilesChange,
}: UseFileUploadProps) {
  const [files, setFiles] = useState<File[]>(field.value || []);
  const [shouldScroll, setShouldScroll] = useState(false);
  const [isDragging, setIsDragging] = useState(false);
  const { showToast } = useToastMessage();
  const t = useTranslations();

  // Update internal state when field value changes (e.g., form reset)
  useEffect(() => {
    // Check if field.value is different from current files state
    const fieldValue = field.value || [];
    const currentFiles = files || [];

    // If lengths are different or field value is empty, update state
    if (fieldValue.length !== currentFiles.length || fieldValue.length === 0) {
      setFiles(fieldValue);
    }
  }, [field.value, files]);

  // Process files (common logic for both file input and drag-drop)
  const processFiles = (newFiles: File[]) => {
    // Check if adding these files would exceed the max files limit
    if (files.length + newFiles.length > maxFiles) {
      showToast(
        "error",
        t("common.form.attachments.error.maxFiles", { count: maxFiles }),
      );
      return false;
    }

    // Check file sizes
    const oversizedFiles = newFiles.filter((file) => file.size > maxSize);
    if (oversizedFiles.length > 0) {
      showToast("error", t("common.form.attachments.error.maxSize"));
      return false;
    }

    // Update state and field value
    const updatedFiles = [...files, ...newFiles];
    setFiles(updatedFiles);
    field.onChange(updatedFiles);

    // Set flag to scroll to the bottom after render
    setShouldScroll(true);

    // Call optional callback
    if (onFilesChange) {
      onFilesChange(updatedFiles);
    }

    return true;
  };

  // Handle file selection from input
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFiles = e.target.files;
    if (!selectedFiles || selectedFiles.length === 0) return;

    // Convert FileList to array
    const newFiles = Array.from(selectedFiles);
    processFiles(newFiles);

    // Reset the input to allow selecting the same file again
    if (e.target) {
      e.target.value = "";
    }
  };

  // Handle drag events
  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(true);
  };

  const handleDragLeave = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);

    if (files.length >= maxFiles) return;

    const droppedFiles = Array.from(e.dataTransfer.files);
    if (droppedFiles.length === 0) return;

    // Filter files by accept attribute if provided
    let filteredFiles = droppedFiles;
    if (accept && accept !== "*/*") {
      const acceptedTypes = accept.split(",").map((type) => type.trim());
      filteredFiles = droppedFiles.filter((file) => {
        // Check if file type matches any of the accepted types
        return acceptedTypes.some((type) => {
          if (type === "*/*") return true;
          if (type.endsWith("/*")) {
            const mainType = type.split("/")[0];
            return file.type.startsWith(mainType + "/");
          }
          return (
            file.type === type ||
            (type.startsWith(".") && file.name.endsWith(type))
          );
        });
      });

      // Show error if some files were filtered out
      if (filteredFiles.length < droppedFiles.length) {
        showToast("error", t("common.form.attachments.error.invalidType"));
      }
    }

    if (filteredFiles.length > 0) {
      processFiles(filteredFiles);
    }
  };

  // Handle file removal
  const handleRemoveFile = (index: number) => {
    const updatedFiles = [...files];
    updatedFiles.splice(index, 1);
    setFiles(updatedFiles);
    field.onChange(updatedFiles);

    // Call optional callback
    if (onFilesChange) {
      onFilesChange(updatedFiles);
    }
  };

  return {
    files,
    isDragging,
    shouldScroll,
    setShouldScroll,
    handleFileChange,
    handleDragOver,
    handleDragLeave,
    handleDrop,
    handleRemoveFile,
  };
}
