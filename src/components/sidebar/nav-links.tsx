"use client";

import { SidebarItem } from "@/types";
import React from "react";
import { Button } from "../ui/button";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { useLocale } from "next-intl";
import { cn } from "@/lib/utils";
import { Locale } from "@/i18n/routing";
import { SYSTEM } from "@/constants/enum";
import { iconMap } from "@/constants";
import { usePermission } from "@/contexts/PermissionContext";

const NavLinks = ({
  items,
  collapse,
}: {
  items: SidebarItem[];
  collapse: boolean;
}) => {
  const locale: Locale = useLocale() as Locale;
  const pathname = usePathname();
  const { hasPermission } = usePermission();
  return (
    <>
      {items && items.length > 0
        ? items.map((item: SidebarItem) => {
            // In src\components\sidebar\nav-links.tsx
            // Update the isActive check to handle nested routes
            const hasAccess =
              item?.permission && item?.permission?.length > 0
                ? item.permission.some((perm) => hasPermission(perm as string))
                : true;

            const isActive =
              item.url === `/${SYSTEM.PEOPLE}/employees`
                ? pathname.replace(`/${locale}`, "").startsWith(item.url) // For employees, check if path starts with the URL
                : pathname.replace(`/${locale}`, "") === item.url;
            return (
              <li key={item.title}>
                <Button
                  asChild
                  variant={"ghost"}
                  // Only spread the variant prop if the link is active
                  {...(isActive && { variant: "linkBackground" })}
                  className={cn(
                    isActive
                      ? "text-black"
                      : "text-[#6B7271] hover:text-black hover:bg-[hsl(var(--primary),0.2)]",
                    "rounded-full h-[50px] w-full flex items-center gap-[15.75px] text-start justify-start",
                    {
                      "justify-center items-center text-center w-[52px] h-[52px]":
                        collapse,
                    },
                    !hasAccess && "hidden",
                  )}
                >
                  <Link href={`/${locale}/${item.url}`}>
                    {item.icon &&
                      iconMap[item.icon] &&
                      React.createElement(iconMap[item.icon], {
                        className: `shrink-0 !w-6 !h-6 hover:text-black ${
                          isActive ? "stroke-black" : "stroke-neutral-500 "
                        }`,
                      })}
                    {!collapse && <span>{item.title}</span>}
                  </Link>
                </Button>
              </li>
            );
          })
        : "There are no items"}
    </>
  );
};

export default NavLinks;
