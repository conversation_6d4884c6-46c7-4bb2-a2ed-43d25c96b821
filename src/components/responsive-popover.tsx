"use client";

import { ReactNode } from "react";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Drawer,
  DrawerContent,
  DrawerTrigger,
  DrawerTitle,
  DrawerDescription,
  DrawerFooter,
} from "@/components/ui/drawer";
import useMediaQuery from "@/hooks/use-media-query";
import { cn } from "@/lib/utils";
import { ScrollArea } from "./ui/scroll-area";
import { LANGUAGES } from "@/constants/enum";
import { useLocale } from "next-intl";
import { Locale } from "@/i18n/routing";

interface ResponsivePopoverProps {
  trigger: ReactNode;
  children: ReactNode;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  align?: "start" | "center" | "end";
  sideOffset?: number;
  className?: string;
  header?: ReactNode;
  headerClassName?: string;
  contentClassName?: string;
  footer?: ReactNode;
  footerContainer?: string;
}

export function ResponsivePopover({
  trigger,
  children,
  open,
  onOpenChange,
  align = "center",
  sideOffset = 4,
  className = "",
  header,
  footer,
  headerClassName = "px-6 py-5 border-b bg-white sticky top-0 z-10",
  footerContainer,
  contentClassName = "p-6 pb-2",
}: ResponsivePopoverProps) {
  const isDesktop = useMediaQuery("(min-width: 768px)");
  const locale: Locale = useLocale() as Locale;
  const isAr = locale === LANGUAGES.ARABIC;

  if (isDesktop) {
    return (
      <>
        {/* Overlay for desktop */}
        {open && (
          <div
            className="fixed inset-0 z-40 bg-black/80 transition-all duration-100"
            style={{ opacity: 0.6 }}
            onClick={() => onOpenChange?.(false)}
          />
        )}
        <Popover open={open} onOpenChange={onOpenChange}>
          <PopoverTrigger asChild>{trigger}</PopoverTrigger>
          <PopoverContent
            className={cn("relative p-0", className)}
            align={align}
            lang={locale}
            dir={isAr ? "rtl" : "ltr"}
            onPointerDownOutside={(e) => {
              e.preventDefault();
            }}
            sideOffset={sideOffset}
          >
            <>
              {/* Header - if provided */}
              {header && <div className={headerClassName}>{header}</div>}

              {/* Content area with scrolling */}
              <ScrollArea
                className="custom-scroll"
                lang={locale}
                dir={isAr ? "rtl" : "ltr"}
              >
                <div className={contentClassName}>{children}</div>
              </ScrollArea>
              {footer && (
                <div
                  className={cn(
                    `sticky bottom-0 left-0 right-0 p-4 border-t bg-white rounded-[20px]`,
                    footerContainer,
                  )}
                >
                  {footer}
                </div>
              )}
            </>
          </PopoverContent>
        </Popover>
      </>
    );
  } else {
    return (
      <Drawer open={open} onOpenChange={onOpenChange}>
        <DrawerTrigger asChild>{trigger}</DrawerTrigger>
        <DrawerContent
          className={cn("p-0 w-full max-h-[96%]")}
          onPointerDownOutside={(e) => {
            e.preventDefault();
          }}
        >
          <DrawerTitle className="sr-only">Dialog Content</DrawerTitle>
          <DrawerDescription className="sr-only">
            Dialog Description
          </DrawerDescription>

          <>
            {/* Header - if provided */}
            {header && <div className={headerClassName}>{header}</div>}

            {/* Content area with scrolling */}
            <ScrollArea className="flex-1 custom-scroll w-full h-full max-h-[85vh]">
              <div
                className={cn("max-h-[calc(85vh-86px)] w-full h-full")}
                lang={locale}
                dir={isAr ? "rtl" : "ltr"}
              >
                {children}
              </div>
            </ScrollArea>
            {footer && (
              <DrawerFooter
                className={cn(
                  "sticky bottom-0 left-0 right-0 p-4 border-t bg-white rounded-[20px]",
                  footerContainer,
                )}
              >
                {footer}
              </DrawerFooter>
            )}
          </>
        </DrawerContent>
      </Drawer>
    );
  }
}
