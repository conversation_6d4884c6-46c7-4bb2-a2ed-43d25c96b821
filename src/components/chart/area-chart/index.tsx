"use client";

import { LANGUAGES } from "@/constants/enum";
import useMediaQuery from "@/hooks/use-media-query";
import { cn } from "@/lib/utils";
import { useLocale } from "next-intl";
import {
  AreaChart,
  Area,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  ReferenceDot,
  Customized,
} from "recharts";
import ChartHeader from "../chart-header";
import {Locale} from "@/i18n/routing";

// Define types for the data and props
interface ChartData {
  [key: string]: any;
  highlight?: boolean;
}

interface ReusableAreaChartProps {
  data: ChartData[];
  xKey: string;
  yKey: string;
  chartWrapperStyle?: string;
  chartStyle?: string;
  chartMargin?: { top: number; right: number; left: number; bottom: number };
  selectData?: {
    selectValue?: string;
    selectOptions?: { value: string; label: string }[];
    onSelectChange?: (value: string) => void;
    selectPlaceholder?: string;
  };
  title?: string;
  titleStyle?: string;
  height?: number;
  width?: string | number;
  yDomain?: [number, number];
  yTicks?: number[];
  primaryColor?: string;
  secondaryColor?: string;
  showGrid?: boolean;
  tooltipFormatter?: (value: any) => [string, string];
}

const ReusableAreaChart: React.FC<ReusableAreaChartProps> = ({
  data,
  xKey,
  yKey,
  title,
  titleStyle,
  chartWrapperStyle,
  chartStyle,
  chartMargin,
  selectData,
  height = 300,
  yDomain = [0, "auto"],
  yTicks,
  primaryColor = "hsl(var(--primary))",
  secondaryColor = "hsl(var(--secondary))",
  showGrid = true,
  tooltipFormatter = (value) => [`${value}`, "Value"],
}) => {
  const locale: Locale = useLocale() as Locale;
  const isAr = locale === LANGUAGES.ARABIC;

  const isMobile = useMediaQuery("(max-width: 767px)");

  const highlightedItems = data.filter((d) => d.highlight);

  return (
    <div style={{ width: "100%", height: "auto" }}>
      <ChartHeader
        title={title}
        titleStyle={titleStyle}
        selectTriggerStyle="max-h-8"
        onSelectChange={selectData?.onSelectChange}
        selectOptions={selectData?.selectOptions}
        selectValue={selectData?.selectValue}
        headerClassName="w-full flex justify-between items-center border-b border-gray-100 px-6 pb-5 pt-4 max-h-[84px] sm:max-h-[66px]"
        selectPlaceholder={selectData?.selectPlaceholder ?? "يناير - فبراير"}
      />

      {/* Chart Container */}
      <div className={cn(chartWrapperStyle)} style={{ width: "100%", height }}>
        <ResponsiveContainer>
          <AreaChart
            data={data}
            className={cn(chartStyle)}
            margin={chartMargin}
          >
            <XAxis
              reversed={isAr}
              dataKey={xKey}
              axisLine={false}
              tick={({ x, y, payload }) => {
                const label = payload.value;
                const matchingData = data.find((item) => item[xKey] === label);
                const isHighlighted = matchingData?.highlight;
                return (
                  <text
                    fill="currentColor"
                    className={`text-sm font-normal leading-5 ${
                      isHighlighted
                        ? "text-secondary font-medium"
                        : "text-gray-400"
                    } `}
                    x={x}
                    y={y + 8}
                    textAnchor="middle"
                  >
                    {label}
                  </text>
                );
              }}
              tickLine={false}
              tickMargin={10}
              interval={isMobile ? 3 : 0}
            />

            <YAxis
              domain={yDomain}
              orientation={isAr ? "right" : "left"}
              ticks={yTicks}
              className="text-sm leading-[14px] font-normal text-gray-400"
              tick={{
                fill: "currentColor",
                fontSize: 14,
              }}
              interval={0}
              axisLine={false}
              tickLine={false}
              tickMargin={20}
            />

            {showGrid && (
              <CartesianGrid
                syncWithTicks={true}
                vertical={false}
                stroke="#E5E6E6"
                strokeDasharray="2 4"
              />
            )}

            <Tooltip formatter={tooltipFormatter} />
            {highlightedItems.length > 0 && (
              <Customized
                component={(props: any) => {
                  const { xAxisMap, yAxisMap } = props;
                  const xAxis = xAxisMap[Object.keys(xAxisMap)[0]];
                  const yAxis = yAxisMap[Object.keys(yAxisMap)[0]];

                  return (
                    <>
                      {highlightedItems.map((item, index) => {
                        const xPos = xAxis.scale(item[xKey]);
                        const yPos = yAxis.scale(item[yKey]);
                        const y0 = yAxis.scale(0);
                        return (
                          <line
                            key={`${item[xKey]}-${index}`}
                            x1={xPos}
                            y1={y0}
                            x2={xPos}
                            y2={yPos}
                            stroke={secondaryColor}
                            strokeDasharray="5 5"
                          />
                        );
                      })}
                    </>
                  );
                }}
              />
            )}

            {highlightedItems.map((item, index) => (
              <ReferenceDot
                key={`${item[xKey]}-${index}`}
                x={item[xKey]}
                y={item[yKey]}
                r={5}
                fill={secondaryColor}
                stroke="#FFFFFF"
                strokeWidth={2}
              />
            ))}

            <defs>
              <linearGradient id="colorGradient" x1="0" y1="0" x2="0" y2="1">
                <stop offset="0%" stopColor={primaryColor} stopOpacity={0.1} />
                <stop
                  offset="100%"
                  stopColor={primaryColor}
                  stopOpacity={0.1}
                />
              </linearGradient>
            </defs>

            <Area
              type={"bump"}
              direction={isAr ? "rtl" : "ltr"}
              dataKey={yKey}
              stroke={secondaryColor}
              strokeWidth={2}
              fill="url(#colorGradient)"
              dot={false}
              activeDot={false}
              animationDuration={500}
            />
          </AreaChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
};

export default ReusableAreaChart;
