"use client";

import FullCalendar from "@fullcalendar/react";
import { EventContentArg } from "@fullcalendar/core";
import dayGridPlugin from "@fullcalendar/daygrid";
import interactionPlugin from "@fullcalendar/interaction";
import timeGridPlugin from "@fullcalendar/timegrid";
import { Button } from "@/components/ui/button";
import useMediaQuery from "@/hooks/use-media-query";
import CalendarSkeleton from "../skeletons";

interface CalendarProps {
  events: any[];
  locale: string;
  isRtl?: boolean;
  loading?: boolean;
  onEventClick?: (eventData: any) => void;
}

export default function Calendar({
  events,
  locale,
  isRtl = false,
  loading = false,
  onEventClick,
}: CalendarProps) {
  const isSmallScreen = useMediaQuery("(max-width:768px)");

  const renderEventContent = (eventInfo: EventContentArg) => (
    <Button
      className="h-full w-full text-start justify-start event-spacing"
      variant="ghost"
      onClick={() =>
        onEventClick && onEventClick(eventInfo.event._def.extendedProps)
      }
    >
      <span className="block mt-1">{eventInfo.event.title}</span>
    </Button>
  );
  // return <CalendarSkeleton />;
  return (
    <div className="relative min-h-dvh overflow-x-auto custom-scroll">
      {loading ? (
        <CalendarSkeleton />
      ) : (
        <FullCalendar
          plugins={[dayGridPlugin, timeGridPlugin, interactionPlugin]}
          editable
          height="auto"
          contentHeight="auto"
          aspectRatio={5}
          eventClassNames={() => "event-spacing"}
          eventContent={renderEventContent}
          dayMaxEventRows={3}
          moreLinkText={(num) =>
            isSmallScreen
              ? isRtl
                ? `${num}+`
                : `+${num}`
              : isRtl
              ? `${num} إضافي...`
              : `${num} more...`
          }
          views={{
            dayGridMonth: {
              dayCellContent: (arg) => (
                <div
                  className={`flex items-center justify-center text-[#727A90] w-8 h-8 mx-auto border rounded-full
                  ${
                    arg.isToday ? "bg-secondary !text-white" : "bg-transparent"
                  }`}
                >
                  {String(arg.date.getDate()).padStart(2, "0")}
                </div>
              ),
            },
          }}
          initialView="dayGridMonth"
          events={events}
          locale={locale}
          direction={isRtl ? "rtl" : "ltr"}
          headerToolbar={{
            start: "placeholder",
            center: "prev title next",
            end: "today",
          }}
          buttonText={{ today: isRtl ? "اليوم" : "Today" }}
        />
      )}
    </div>
  );
}
