import { SALARY_STATUS } from "@/constants/enum";

const statusBackgroundColors = {
  [SALARY_STATUS.PAID]: "bg-success-50",
  [SALARY_STATUS.APPROVED]: "bg-success-50",
  [SALARY_STATUS.REJECTED]: "bg-red-50",
  [SALARY_STATUS.DRAFT]: "bg-neutral-100",
  [SALARY_STATUS.SUBMITTED]: "bg-blue-50",
};

const statusTextColors = {
  [SALARY_STATUS.PAID]: "text-green-900",
  [SALARY_STATUS.APPROVED]: "text-green-900",
  [SALARY_STATUS.REJECTED]: "text-red-500",
  [SALARY_STATUS.DRAFT]: "text-neutral-600",
  [SALARY_STATUS.SUBMITTED]: "text-blue-700",
};

const SalaryStatus = ({
  status,
  label,
}: {
  status: SALARY_STATUS;
  label: string;
}) => {
  // Default to neutral styling if status is not recognized
  const bgColor = statusBackgroundColors[status] || "bg-neutral-100";
  const textColor = statusTextColors[status] || "text-neutral-600";

  return (
    <div
      className={`min-w-24 max-w-24 min-h-[27px] text-center leading-[27px] px-3 rounded-sm text-sm font-semibold tracking-tight ${bgColor} ${textColor}`}
    >
      {label}
    </div>
  );
};

export default SalaryStatus;
