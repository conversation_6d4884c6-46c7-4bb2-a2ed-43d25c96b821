"use client";

import { Check } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import { TbClockHour8Filled } from "react-icons/tb";
import { PiListChecksLight } from "react-icons/pi";
import { HiArrowLongLeft } from "react-icons/hi2";
import { CircleAlert } from "../../../../public/images/icons";
import { cn } from "@/lib/utils";
import { useLocale, useTranslations } from "next-intl";
import Link from "next/link";
import { Notification, TSystems } from "@/types";
import { useSystem } from "@/contexts/system-provider";
import {Locale} from "@/i18n/routing";

type NotificationsListProps = {
  containerStyle?: string;
  notifications: Notification[];
  markAsRead: (id: number) => void;
  markAllAsRead: () => void;
  onSeeMore?: () => void;
};

export default function NotificationsList({
  containerStyle,
  notifications,
  markAsRead,
  markAllAsRead,
  onSeeMore,
}: NotificationsListProps) {
  const t = useTranslations();
  const locale: Locale = useLocale() as Locale;
  const { currSystem } = useSystem();

  const dyanmicMessage = t(
    `common.${currSystem}.notification.message` as `common.${TSystems}.notification.message`
  );
  const seeMoreLink = `/${locale}/${currSystem}/notifications`;
  return (
    <>
      <ScrollArea className={cn("h-[466px]", containerStyle)}>
        <div className="space-y-1">
          {notifications.map((notification) => (
            <Card
              key={notification.id}
              className="border-0 shadow-none"
            >
              <CardContent className="p-0">
                <div
                  className={cn(
                    "flex rtl:justify-end items-start gap-3 p-4 min-h-[146px] hover:bg-background-v2 rounded-lg",
                    { "bg-background-v2": !notification.isRead }
                  )}
                >
                  <div className="space-y-1.5 flex-1">
                    <div className="flex items-center rtl:flex-row-reverse">
                      <p className="text-xs font-medium text-muted-foreground text-[#727A90] flex items-center gap-2">
                        <span className="flex gap-1 rtl:flex-row-reverse">
                          <TbClockHour8Filled className="!w-[14px] h-[14px]" />
                          {notification.date}
                        </span>
                        {!notification.isRead && (
                          <span className="flex rtl:flex-row-reverse items-center gap-1">
                            <CircleAlert className="w-[14px] h-[14px]" />
                            {dyanmicMessage}
                          </span>
                        )}
                      </p>
                    </div>
                    <h3 className="font-semibold text-base leading-none text-black">
                      {notification.title}
                    </h3>
                    <p className="text-sm mb-2 text-muted-foreground text-[#686F83] leading-5 tracking-[0.5%]">
                      {notification.description}
                    </p>
                    {!notification.isRead && (
                      <Button
                        onClick={() => markAsRead(notification.id)}
                        variant={"linkUnderLine"}
                        className="flex ltr:ms-auto rtl:ms-0 items-center gap-1 font-bold text-sm p-0"
                      >
                        <Check className="!w-5 !h-5 font-normal stroke-[1.5px]" />
                        {t("common.navbar.notification.markAsRead")}
                      </Button>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </ScrollArea>

      {/* Action Buttons */}
      <div className="p-4 flex justify-between items-center border-t max-h-[52px]">
        <Button
          variant="linkUnderLine"
          className="text-sm font-bold flex items-center gap-1 p-0"
          onClick={markAllAsRead}
        >
          <PiListChecksLight width={20} height={20} className="!w-5 !h-5" />
          {t("common.navbar.notification.markAllAsRead")}
        </Button>
        {onSeeMore && (
          <Link href={seeMoreLink}>
            <Button
              variant="linkUnderLine"
              className="text-sm font-bold flex items-center gap-2 p-0"
              onClick={onSeeMore}
            >
              {t("common.navbar.notification.seeMore")}
              <HiArrowLongLeft className="!w-5 !h-5 rtl:rotate-0 ltr:rotate-180" />
            </Button>
          </Link>
        )}
      </div>
    </>
  );
}
