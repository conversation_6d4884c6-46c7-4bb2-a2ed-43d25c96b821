"use client";

/**
 * Determines if the current path is an employee profile page
 */
export function useIsEmployeeProfilePage(pathname: string): boolean {
  const pathSegments = pathname.split("/");

  return (
    pathSegments.length >= 4 &&
    pathSegments.includes("people") &&
    pathSegments.includes("employees") &&
    // Main profile page (e.g., /people/employees/123)
    (pathSegments[pathSegments.length - 2] === "employees" ||
      // Tab pages (e.g., /people/employees/123/salary)
      (pathSegments.length >= 5 &&
        pathSegments[pathSegments.length - 3] === "employees" &&
        [
          "salary",
          "attachments",
          "leaves",
          "overtime",
          "roles-projects",
        ].includes(pathSegments[pathSegments.length - 1])))
  );
}

/**
 * Gets the page title based on the current path
 */
export function getPageTitle(
  pathname: string,
  isEmployeeProfilePage: boolean,
): string {
  const pathSegments = pathname.split("/");

  if (isEmployeeProfilePage) {
    return "employees";
  }

  return pathSegments[pathSegments.length - 1] as
    | "people"
    | "procure"
    | "cm"
    | "patients"
    | "appointments"
    | "settings"
    | "employees";
}
