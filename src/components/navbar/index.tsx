"use client";

import { useLocale, useTranslations } from "next-intl";
import { usePathname, useSearchParams } from "next/navigation";
import React from "react";
import { SidebarData } from "@/types";
import { Locale } from "@/i18n/routing";
import EmployeeNavbarHeader from "@/app/[locale]/people/employees/[id]/employee-navbar-header";
import StandardHeader from "./headers/standard-header";
import DesktopNavigation from "./navigation/desktop-navigation";
import MobileNavigation from "./navigation/mobile-navigation";
import MobileTitle from "./mobile-title";
import { useIsEmployeeProfilePage, getPageTitle } from "./utils/path-utils";
import { MobileMenuProvider } from "@/contexts/mobile-menu-context";

type NavbarProps = {
  data: SidebarData;
};

const Navbar: React.FC<NavbarProps> = ({ data }) => {
  const t = useTranslations();
  const pathname = usePathname();
  const currentTab = useSearchParams().get("tab");
  const locale: Locale = useLocale() as Locale;

  // Helper functions for path analysis
  const isEmployeeProfilePage = useIsEmployeeProfilePage(pathname);
  const pageTitle = getPageTitle(pathname, isEmployeeProfilePage);

  // Get translations
  const translationKey = `common.sidebar.links.${pageTitle}` as const;
  const translatedTitle = t(translationKey);

  const tabKey = currentTab as "global" | "change-password" | "notifications";
  const translatedTabKey = `common.settings.tabs.${tabKey}` as const;
  const translatedTabs = currentTab ? t(translatedTabKey) : "";

  // Handler for search input changes
  const handleSearch = (value: string) => {
    console.log("User searched for:", value);
    // You can perform search logic here, e.g., update state or trigger API call
  };

  return (
    <MobileMenuProvider>
      <nav className="flex items-center container max-md:px-2 max-md:py-3 md:mb-5 justify-between max-md:border-b max-lg:h-[64px]">
        <header>
          {isEmployeeProfilePage ? (
            <EmployeeNavbarHeader />
          ) : (
            <StandardHeader
              title={translatedTitle}
              data={data}
              locale={locale}
            />
          )}
        </header>

        {/* Desktop Navigation */}
        <DesktopNavigation
          searchPlaceholder={t("common.navbar.search.placeholder")}
          onSearch={handleSearch}
        />

        {/* Mobile Navigation */}
        <MobileNavigation data={data} />
      </nav>

      {/* Mobile Title */}
      {!isEmployeeProfilePage && (
        <MobileTitle
          currentTab={currentTab}
          translatedTabs={translatedTabs}
          translatedTitle={translatedTitle}
        />
      )}
    </MobileMenuProvider>
  );
};

export default Navbar;
