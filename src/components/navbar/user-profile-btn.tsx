"use client";

import { useL<PERSON>ale, useTranslations } from "next-intl";
import { But<PERSON> } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { ChevronDown } from "lucide-react";
import {
  Pencil,
  Lock,
  SignOut,
  Settings as SettingsIcon,
} from "../../../public/images/icons";
import { startTransition, useActionState, useEffect, useState } from "react";
import { LANGUAGES } from "@/constants/enum";
import { useSettingsModal } from "@/contexts/settings-modal-context";
import dynamic from "next/dynamic";
import Loader from "../loader";
import { Locale } from "@/i18n/routing";
import { onSubmitLogout } from "@/server/actions/auth";
import { useToastMessage } from "@/hooks/use-toast-message";

import { ActionState } from "@/types";
import { useUser } from "@/contexts/user-provider";
import { Skeleton } from "../ui/skeleton";
import { usePermission } from "@/contexts/PermissionContext";
import { performLogout } from "@/utils/logout";

const SettingsModal = dynamic(() => import("./settings-modal"), {
  loading: () => <Loader overlayColor="#000" overlayOpacity={0.6} />,
  ssr: false,
});
export default function UserProfileButton() {
  const t = useTranslations();
  const locale: Locale = useLocale() as Locale;
  const isAr = locale === LANGUAGES.ARABIC;
  const { showToast } = useToastMessage();

  const { user, mutateUser } = useUser();
  const { clearPermissions } = usePermission();

  // Initial state for logout action
  const initialState: ActionState<{ redirectTo: string }> = {
    error: "",
    success: "",
    issues: [],
    redirectTo: "",
  };

  const [state, logoutAction, isPending] = useActionState(
    onSubmitLogout,
    initialState,
  );

  // Local dropdown state
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);

  // Get modal state and openSettings from context
  const {
    openSettings,
    settingsModalOpen,
    setSettingsModalOpen,
    settingsActiveTab,
    settingsSubForm,
  } = useSettingsModal();

  useEffect(() => {
    if (state.success) {
      showToast("success", state.success);
      // Force a full page reload to trigger middleware
      window.location.href = state.redirectTo || `/${locale}/auth/login`;
    }
  }, [state.success]);

  // Logout functionality
  const handleLogout = async () => {
    setIsDropdownOpen(false);

    await performLogout(clearPermissions, mutateUser, () => {
      startTransition(() => {
        logoutAction();
      });
    });
  };

  // Add loading state check
  if (!user) {
    return (
      <div className="flex items-center gap-2 max-w-[137px] w-full">
        <Skeleton className="h-[38px] w-[38px] rounded-full bg-gray-200" />
        <div className="flex flex-col gap-1">
          <Skeleton className="h-3 w-20 bg-gray-200" />
          <Skeleton className="h-2 w-16 bg-gray-200" />
        </div>
      </div>
    );
  }

  return (
    <>
      {/* Backdrop for dropdown */}
      {isDropdownOpen && (
        <div
          className="fixed inset-0 bg-black/30 z-40"
          onClick={() => setIsDropdownOpen(false)}
        />
      )}

      <DropdownMenu onOpenChange={setIsDropdownOpen}>
        <DropdownMenuTrigger asChild>
          <Button
            variant="ghost"
            className={`relative h-10 flex gap-1 items-center rounded-full p-0 ${
              isDropdownOpen ? "z-50" : "z-10"
            }`}
          >
            <Avatar className="w-[38px] h-[38px]">
              <AvatarImage
                className="object-cover object-top"
                src={user.avatar || "/default-avatar.png"}
                alt="User Avatar"
              />
              <AvatarFallback>{user?.name?.charAt(0) || "U"}</AvatarFallback>
            </Avatar>
            <div className="flex flex-col gap-1 text-start">
              <span className="font-bold text-xs">{user?.name}</span>
            </div>
            <ChevronDown className="!w-6 !h-6 text-[#292D32]" />
          </Button>
        </DropdownMenuTrigger>

        <DropdownMenuContent
          lang={locale}
          align={isAr ? "start" : "end"}
          className="w-60 z-50 flex flex-col max-w-[240px] h-[196px] gap-1 p-3 text-[#727A90] rounded-xl"
        >
          {/* Edit Profile opens GENERAL tab with the EDIT_PROFILE form */}
          <DropdownMenuItem
            className="rtl:flex-row-reverse hover:!bg-background-v2 hover:!text-secondary h-10 cursor-pointer"
            onClick={() => openSettings("GENERAL", "EDIT_PROFILE")}
          >
            <Pencil className="hover:text-secondary-dark" />
            {t("common.navbar.userProfile.editProfile")}
          </DropdownMenuItem>

          {/* Edit Password opens GENERAL tab with the CHANGE_PASSWORD form */}
          <DropdownMenuItem
            className="rtl:flex-row-reverse hover:!bg-background-v2 hover:!text-secondary h-10 cursor-pointer"
            onClick={() => openSettings("GENERAL", "CHANGE_PASSWORD")}
          >
            <Lock className="hover:text-secondary-dark !w-[18px] !h-5" />
            {t("common.navbar.userProfile.editPassword")}
          </DropdownMenuItem>

          {/* Settings opens the NOTIFICATIONS tab */}
          <DropdownMenuItem
            className="rtl:flex-row-reverse hover:!bg-background-v2 hover:!text-secondary h-10 cursor-pointer"
            onClick={() => openSettings("NOTIFICATIONS")}
          >
            <SettingsIcon className="hover:text-secondary-dark" />
            {t("common.navbar.userProfile.settings")}
          </DropdownMenuItem>

          {/* Logout */}
          <DropdownMenuItem
            className="rtl:flex-row-reverse text-error font-bold hover:!bg-background-v2 hover:!text-secondary h-10 cursor-pointer"
            onClick={handleLogout}
          >
            <SignOut className="hover:text-secondary-dark" />
            {t("common.navbar.userProfile.logout")}
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      {/* Render the Settings Modal (for desktop) */}
      {settingsModalOpen && (
        <SettingsModal
          open={settingsModalOpen}
          activeTab={settingsActiveTab}
          subForm={settingsSubForm}
          openSettings={openSettings}
          onClose={() => setSettingsModalOpen(false)}
        />
      )}
    </>
  );
}
