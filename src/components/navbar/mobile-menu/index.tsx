"use client";

import React from "react";
import {
  Drawer,
  Drawer<PERSON>ontent,
  Drawer<PERSON>lose,
  DrawerOverlay,
  DrawerTitle,
} from "@/components/ui/drawer";
import { useMobileMenu } from "@/contexts/mobile-menu-context";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Button } from "@/components/ui/button";
import { useLocale, useTranslations } from "next-intl";
import { LANGUAGES } from "@/constants/enum";
import { Locale } from "@/i18n/routing";
import { SidebarData } from "@/types";
import { XIcon } from "lucide-react";
import MobileMenuHeader from "./mobile-menu-header";
import MobileMenuNavLinks from "./mobile-menu-nav-links";
import MobileMenuFooter from "./mobile-menu-footer";

type MobileMenuProps = {
  data: SidebarData;
};

const MobileMenu: React.FC<MobileMenuProps> = ({ data }) => {
  const { isMenuOpen, closeMenu, isMobileScreen } = useMobileMenu();
  const locale: Locale = useLocale() as Locale;
  const isAr = locale === LANGUAGES.ARABIC;
  const t = useTranslations();

  // Only render the drawer on mobile screens
  if (!isMobileScreen) {
    return null;
  }

  return (
    <Drawer open={isMenuOpen} onOpenChange={closeMenu}>
      <DrawerOverlay className="bg-black/60" />
      <DrawerContent className="h-[100dvh] max-h-[100dvh] rounded-t-[20px]">
        <DrawerTitle className="sr-only">
          {t("common.navbar.mobileMenu.title") || "Mobile Menu"}
        </DrawerTitle>
        <div className="flex justify-end p-4">
          <DrawerClose asChild>
            <Button
              variant="ghost"
              size="icon"
              className="h-8 w-8 rounded-full border border-neutral-200"
            >
              <XIcon className="h-4 w-4 text-secondary" />
              <span className="sr-only">{t("common.buttonText.cancel")}</span>
            </Button>
          </DrawerClose>
        </div>

        <ScrollArea className="h-full pb-8">
          <div
            className="px-6 flex flex-col h-full"
            lang={locale}
            dir={isAr ? "rtl" : "ltr"}
          >
            {/* Header with system name and icon */}
            <MobileMenuHeader data={data} />

            {/* Navigation Links */}
            <div className="mt-6 flex-1">
              <MobileMenuNavLinks items={data.items || []} />
            </div>

            {/* Footer with settings and system selector */}
            <MobileMenuFooter />
          </div>
        </ScrollArea>
      </DrawerContent>
    </Drawer>
  );
};

export default MobileMenu;
