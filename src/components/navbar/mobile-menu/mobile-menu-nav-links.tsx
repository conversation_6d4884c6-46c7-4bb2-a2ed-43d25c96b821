"use client";

import React from "react";
import { SidebarItem } from "@/types";
import { Button } from "@/components/ui/button";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { useLocale } from "next-intl";
import { iconMap } from "@/constants";
import { cn } from "@/lib/utils";
import { Locale } from "@/i18n/routing";
import { SYSTEM } from "@/constants/enum";
import { useMobileMenu } from "@/contexts/mobile-menu-context";

interface MobileMenuNavLinksProps {
  items: SidebarItem[];
}

const MobileMenuNavLinks: React.FC<MobileMenuNavLinksProps> = ({ items }) => {
  const locale: Locale = useLocale() as Locale;
  const pathname = usePathname();
  const { closeMenu } = useMobileMenu();

  if (!items || items.length === 0) {
    return null;
  }

  return (
    <ul className="space-y-2">
      {items.map((item: SidebarItem) => {
        // Update the isActive check to handle nested routes
        const isActive =
          item.url === `/${SYSTEM.PEOPLE}/employees`
            ? pathname.replace(`/${locale}`, "").startsWith(item.url) // For employees, check if path starts with the URL
            : pathname.replace(`/${locale}`, "") === item.url; // For other items, exact match

        return (
          <li key={item.title}>
            <Button
              asChild
              variant={isActive ? "linkBackground" : "ghost"}
              className={cn(
                isActive
                  ? "text-black"
                  : "text-[#6B7271] hover:text-black hover:bg-[hsl(var(--primary),0.2)]",
                "rounded-full h-[50px] w-full flex items-center gap-4 text-start justify-start",
              )}
              onClick={closeMenu}
            >
              <Link href={`/${locale}${item.url}`}>
                <span className="flex items-center gap-4">
                  {item.icon &&
                    iconMap[item.icon] &&
                    React.createElement(iconMap[item.icon], {
                      className: cn(
                        "!w-6 !h-6",
                        isActive ? "text-secondary" : "text-[#6B7271]",
                      ),
                    })}
                  <span className="font-medium">{item.title}</span>
                </span>
              </Link>
            </Button>
          </li>
        );
      })}
    </ul>
  );
};

export default MobileMenuNavLinks;
