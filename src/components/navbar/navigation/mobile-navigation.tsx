"use client";

import React from "react";
import { Button } from "@/components/ui/button";
import { Menu } from "lucide-react";
import { useMobileMenu } from "@/contexts/mobile-menu-context";
import MobileMenu from "../mobile-menu";
import { SidebarData } from "@/types";
import ClientOnly from "@/components/client-only";

type MobileNavigationProps = {
  data: SidebarData;
};

const MobileNavigation: React.FC<MobileNavigationProps> = ({ data }) => {
  return (
    <div className="md:hidden h-[40px] flex items-center justify-end">
      <ClientOnly fallback={
        <div className="w-[40px] h-[40px] flex items-center justify-center">
          <Button
            variant="ghost"
            className="p-2 opacity-0"
            disabled
            aria-label="Toggle mobile menu"
          >
            <Menu className="text-secondary !w-7 !h-6" />
          </Button>
        </div>
      }>
        <MobileNavigationContent data={data} />
      </ClientOnly>
    </div>
  );
};

const MobileNavigationContent: React.FC<MobileNavigationProps> = ({ data }) => {
  const { toggleMenu, isMobileScreen } = useMobileMenu();

  return (
    <>
      <div className="w-[40px] h-[40px] flex items-center justify-center">
        <Button
          variant="ghost"
          className={`p-2 ${!isMobileScreen ? 'opacity-0' : ''}`}
          onClick={toggleMenu}
          aria-label="Toggle mobile menu"
          disabled={!isMobileScreen}
        >
          <Menu className="text-secondary !w-7 !h-6" />
        </Button>
      </div>

      {isMobileScreen && <MobileMenu data={data} />}
    </>
  );
};

export default MobileNavigation;
