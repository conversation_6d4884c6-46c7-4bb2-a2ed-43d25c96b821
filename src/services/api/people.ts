import { buildApiUrl } from "@/utils/api";
import { BaseAPI } from ".";
import {
  TEmployee,
  TEmployeeAttachment,
  TEmployeeAttachmentsResponse,
  TEmployeeResponse,
  TEmployeesResponse,
} from "@/app/[locale]/_modules/people/type/employee";
import { AttendanceEventResponse, EmployeeLeavesResponse } from "@/app/[locale]/_modules/people/type/employee-leaves";
import { ApprovalRequestResponse } from "@/app/[locale]/_modules/people/type/approval-request";
import { TSalaryPackageResponse } from "@/app/[locale]/_modules/people/type/salary-package";
import { cookies, headers } from "next/headers";
import { MetricCardResponse } from "@/types";
import { format, startOfMonth } from "date-fns";

/**
 * PeopleService class for handling all people-related API requests
 */
export class PeopleService extends BaseAPI {
  constructor() {
    super(buildApiUrl("people"));
  }

  // Get all employees with pagination and sorting
  async getEmployees(
    page: number = 1,
    limit: number = 5,
    sort: string = "start_date",
    search: string = "",
    filters: string = "",
  ) {
    const peoplesessionToken = await getPeopleSessionToken();

    let url = `/api/employees?sort=${sort}&page[number]=${page}&page[size]=${limit}`;
    if (search) {
      url += `&filter[search]=${encodeURIComponent(search)}`;
    }
    if (filters) {
      url += `&${filters}`; //for modal filters
    }

    const response = await this.request<TEmployeesResponse>(url, {
      headers: { Authorization: `Bearer ${peoplesessionToken}` },
      method: "GET",
    });

    return response;
  }

  // Get a specific employee by ID
  async getEmployeeById(id: string, include?: string) {
    const peoplesessionToken = await getPeopleSessionToken();

    let url = `/api/employees/${id}`;
    if (include) {
      url += `?include=${include}`;
    }

    const response = await this.request<TEmployeeResponse>(url, {
      headers: { Authorization: `Bearer ${peoplesessionToken}` },
      method: "GET",
    });

    return response;
  }

  async getEmployeeMonthStatistics(id: string) {
    const peoplesessionToken = await getPeopleSessionToken();

    const response = await this.request<TEmployeeResponse>(
      `/api/employees/${id}/attendance/periods/month_statistics`,
      {
        headers: { Authorization: `Bearer ${peoplesessionToken}` },
        method: "GET",
      },
    );

    return response;
  }

  // Get Day Summary for employee
  async getDaySummary(id: string, date: string) {
    const peoplesessionToken = await getPeopleSessionToken();

    const response = await this.request<TEmployeeResponse>(
      `/api/employees/${id}/attendance/periods/daily_records?date=${date}`,
      {
        headers: {
          Authorization: `Bearer ${peoplesessionToken}`,
          ContentType: "application/json",
        },
        method: "GET",
      },
    );

    return response;
  }

  // Get employee attachments
  async getEmployeeAttachments(employeeId: string) {
    const peoplesessionToken = await getPeopleSessionToken();
    const response = await this.request<TEmployeeAttachmentsResponse>(
      `/api/employees/${employeeId}/attachment`,
      {
        headers: { Authorization: `Bearer ${peoplesessionToken}` },
        method: "GET",
      },
    );
    return response;
  }

  async getEmployeeStatistics(
    employeeId: string,
    metricArray: string[] = [],
    period: string = "month",
    startDate: string = format(startOfMonth(new Date()), "dd-MM-yyyy"),
    endDate?: string,
  ) {
    const peopleSessionToken = await getPeopleSessionToken();

    const params = new URLSearchParams({
      [`context[employee_id]`]: employeeId,
      [`context[comparison_period]`]: period,
      [`context[start_date]`]: startDate,
    });

    if (endDate) {
      params.append("context[end_date]", endDate);
    }

    metricArray.forEach((metric) => {
      params.append("filter[metric_key_in][]", metric);
    });

    const response = await this.request<MetricCardResponse>(
      `/api/statistics?${params.toString()}`,
      {
        headers: { Authorization: `Bearer ${peopleSessionToken}` },
        method: "GET",
      },
    );
    return response;
  }

  //  Rename an employee attachment
  async renameAttachment(
    employeeId: string,
    attachmentId: string,
    newName: string,
  ) {
    type RenameResponse = {
      data: TEmployeeAttachment;
    };

    const formData = new FormData();
    formData.append("attachment[filename]", newName);
    const peoplesessionToken = await getPeopleSessionToken();

    const response = await this.requestWithFormData<RenameResponse>(
      `/api/employees/${employeeId}/attachment/${attachmentId}`,
      {
        headers: { Authorization: `Bearer ${peoplesessionToken}` },
        method: "PUT",
        body: formData,
      },
    );
    return response.data;
  }

  // Delete an employee attachment
  async deleteAttachment(employeeId: string, attachmentId: string) {
    const peoplesessionToken = await getPeopleSessionToken();
    await this.request(
      `api/employees/${employeeId}/attachment/${attachmentId}`,
      {
        headers: { Authorization: `Bearer ${peoplesessionToken}` },
        method: "DELETE",
      },
    );
    return { success: true };
  }

  // Add a new employee
  async addNewEmployee(formData: FormData) {
    try {
      const peoplesessionToken = await getPeopleSessionToken();
      const apiFormData = new FormData();

      for (const [key, value] of formData.entries()) {
        let formattedKey;

        if (key === "attachments[]") {
          formattedKey = `employee[attachments][]`;
        } else if (key.startsWith("employee[user_roles_list]")) {
          // Keep the user_roles_list format as is
          formattedKey = key;
        } else {
          formattedKey = `employee[${key}]`;
        }

        apiFormData.append(formattedKey, value);
      }

      const response = await this.requestWithFormData<TEmployee>(
        "/api/employees",
        {
          headers: { Authorization: `Bearer ${peoplesessionToken}` },
          method: "POST",
          body: apiFormData,
        },
      );

      return response;
    } catch (error) {
      throw error;
    }
  }

  // Get employee leaves statistics
  async useEmployeeLeavesStats(employeeId: string) {
    const peoplesessionToken = await getPeopleSessionToken();

    const response = await this.request(
      `/api/employees/${employeeId}/leaves-stats`,
      {
        headers: { Authorization: `Bearer ${peoplesessionToken}` },
        method: "GET",
      },
    );

    return response;
  }

  async getGlobalLeavesData(
    page: number = 1,
    limit: number = 10,
    include: string,
    search: string = "",
    sort: string = "-updated_at",
    filters: string = "",
  ) {
    const peoplesessionToken = await getPeopleSessionToken();

    let url = `api/leaves?page[number]=${page}&page[size]=${limit}&sort=${sort}`;

    if (search) {
      url += `&filter[search]=${encodeURIComponent(search)}`;
    }

    if (include) {
      url += `&include=${include}`;
    }

    if (filters) {
      url += `&${filters}`;
    }

    const response = await this.request<EmployeeLeavesResponse>(url, {
      headers: { Authorization: `Bearer ${peoplesessionToken}` },
      method: "GET",
    });

    return response;
  }

  async getEmployeeLeaveDetails(
    employeeId: string,
    page: number = 1,
    limit: number = 5,
  ) {
    const peoplesessionToken = await getPeopleSessionToken();

    const response = await this.request<EmployeeLeavesResponse>(
      `api/employees/${employeeId}/leaves?page[number]=${page}&page[size]=${limit}`,
      {
        headers: { Authorization: `Bearer ${peoplesessionToken}` },
        method: "GET",
      },
    );

    return response;
  }

  // Update leave dates
  async updateLeaveDates(
    employeeId: string,
    leaveId: string,
    startDate: string,
    endDate: string,
    leaveDuration?: string,
  ) {
    try {
      const peoplesessionToken = await getPeopleSessionToken();

      const formData = new FormData();
      formData.append("leave[start_date]", startDate);
      formData.append("leave[end_date]", endDate);

      if (leaveDuration) {
        formData.append("leave[leave_duration]", leaveDuration);
      }

      await this.requestWithFormData(
        `/api/employees/${employeeId}/leaves/${leaveId}`,
        {
          headers: { Authorization: `Bearer ${peoplesessionToken}` },
          method: "PUT",
          body: formData,
        },
      );

      return {
        data: {
          id: leaveId,
          startDate,
          endDate,
          updatedAt: new Date().toISOString(),
        },
      };
    } catch (error) {
      throw error;
    }
  }
  async getPeopleAttendance(params: string) {
    let peoplesessionToken = await getPeopleSessionToken();
    let headerToken: string | null = null;
    // If not found in cookies, try to get from headers (for API routes)
    if (!peoplesessionToken) {
      const reqHeaders = headers();
      headerToken =
        (await reqHeaders).get("authorization")?.replace(/^Token\s+/i, "") ||
        null;
    }

    // Build the URL with or without employee filter
    let url = `api/attendance/events?sort=-id&${params}`;

    const response = await this.request<AttendanceEventResponse>(url, {
      headers: {
        Authorization: peoplesessionToken
          ? `Bearer ${peoplesessionToken}`
          : `${headerToken}`,
      },
      method: "GET",
    });

    return response;
  }

  async getSalaryCalculations(
    page: number = 1,
    limit: number = 5,
    employeeId: string,
  ) {
    const peoplesessionToken = await getPeopleSessionToken();

    // Build the URL with or without employee filter
    let url = `api/finance/salary_calculations?sort=-id&page[number]=${page}&page[size]=${limit}`;
    if (employeeId) {
      url += `&filter[employee_id_eq]=${employeeId}`;
    } else {
      url += `&include=employee`;
    }

    const response = await this.request<EmployeeLeavesResponse>(url, {
      headers: { Authorization: `Bearer ${peoplesessionToken}` },
      method: "GET",
    });

    return response;
  }

  // Calculate salary for a specific period
  async calculateSalaryPeriod(period: string) {
    try {
      const peoplesessionToken = await getPeopleSessionToken();

      const response = await this.request(
        `api/finance/salary_calculations/calculate_period?period=${period}`,
        {
          headers: { Authorization: `Bearer ${peoplesessionToken}` },
          method: "POST",
        },
      );

      return response;
    } catch (error) {
      throw error;
    }
  }

  // Update salary status (approve/reject)
  async updateSalaryStatus(salaryId: string, status: string) {
    try {
      const peoplesessionToken = await getPeopleSessionToken();

      const response = await this.request(
        `api/finance/salary_calculations/${salaryId}`,
        {
          headers: {
            Authorization: `Bearer ${peoplesessionToken}`,
            "Content-Type": "application/json",
          },
          method: "PATCH",
          body: JSON.stringify({ status }),
        },
      );

      return response;
    } catch (error) {
      throw error;
    }
  }

  // Common method for handling approval and rejection
  async handleApprovalRequest(
    approvalRequestId: string,
    action: "approve" | "reject",
    comment: string = "",
  ) {
    try {
      const peoplesessionToken = await getPeopleSessionToken();

      const endpoint = `/api/approval_requests/${approvalRequestId}/${action}${
        comment ? `?comment=${encodeURIComponent(comment)}` : ""
      }`;

      const response = await this.request<ApprovalRequestResponse>(endpoint, {
        headers: { Authorization: `Bearer ${peoplesessionToken}` },
        method: "POST",
      });

      // The response contains the full approval request data
      // We'll return both the simplified status info and the full response
      return {
        data: {
          id: approvalRequestId,
          status: action === "approve" ? "approved" : "rejected",
          updatedAt: new Date().toISOString(),
          approvalRequest: response.data, // Include the full approval request data
        },
      };
    } catch (error) {
      throw error;
    }
  }

  // Convenience methods that use the common handler
  async approveLeaveRequest(approvalRequestId: string, comment: string = "") {
    return this.handleApprovalRequest(approvalRequestId, "approve", comment);
  }

  // This is used by the new API directly
  async _rejectLeaveRequest(approvalRequestId: string, comment: string = "") {
    return this.handleApprovalRequest(approvalRequestId, "reject", comment);
  }

  async withdrawLeaveRequest(employeeId: string, leaveId: string) {
    try {
      const peoplesessionToken = await getPeopleSessionToken();

      const endpoint = `/api/employees/${employeeId}/leaves/${leaveId}/withdraw`;

      await this.request(endpoint, {
        headers: { Authorization: `Bearer ${peoplesessionToken}` },
        method: "POST",
      });

      return {
        data: {
          id: leaveId,
          status: "withdrawn",
          updatedAt: new Date().toISOString(),
        },
      };
    } catch (error) {
      throw error;
    }
  }

  // Legacy methods for backward compatibility
  async updateLeaveStatus(
    employeeId: string,
    leaveId: string,
    status: "withdrawn" | "approved" | "rejected",
    approvalRequestId?: string,
    comment: string = "",
  ) {
    try {
      if (status === "withdrawn") {
        return this.withdrawLeaveRequest(employeeId, leaveId);
      } else if (status === "approved" && approvalRequestId) {
        return this.approveLeaveRequest(approvalRequestId, comment);
      } else if (status === "rejected" && approvalRequestId) {
        return this._rejectLeaveRequest(approvalRequestId, comment);
      } else {
        throw new Error("Invalid status or missing approval request ID");
      }
    } catch (error) {
      throw error;
    }
  }

  async getLeaveAttachments(
    leaveId: string,
    page: number = 1,
    limit: number = 20,
  ) {
    try {
      const peoplesessionToken = await getPeopleSessionToken();

      const endpoint = `/api/leaves/${leaveId}/documents?page[number]=${page}&page[size]=${limit}`;

      const response = await this.request<TEmployeeAttachmentsResponse>(
        endpoint,
        {
          headers: { Authorization: `Bearer ${peoplesessionToken}` },
          method: "GET",
        },
      );

      return response;
    } catch (error) {
      throw error;
    }
  }

  // Create a new leave
  async createLeave(employeeId: string, formData: FormData) {
    try {
      const peoplesessionToken = await getPeopleSessionToken();

      const apiFormData = new FormData();

      for (const [key, value] of formData.entries()) {
        let formattedKey;

        if (key === "documents[]") {
          formattedKey = `leave[documents][]`;
        } else {
          formattedKey = `leave[${key}]`;
        }

        // Only append the value once
        apiFormData.append(formattedKey, value);
      }

      const response = await this.requestWithFormData(
        `/api/employees/${employeeId}/leaves`,
        {
          headers: { Authorization: `Bearer ${peoplesessionToken}` },
          method: "POST",
          body: apiFormData,
        },
      );

      return response;
    } catch (error) {
      throw error;
    }
  }

  async saveSalaryPackage(formData: FormData) {
    try {
      const peoplesessionToken = await getPeopleSessionToken();
      const apiFormData = new FormData();

      for (const [key, value] of formData.entries()) {
        const formattedKey = `salary_package[${key}]`;
        apiFormData.append(formattedKey, value);
      }

      // Use the same endpoint for both create and update
      const url = `/api/finance/salary_packages`;

      const method = "POST";

      const response = await this.requestWithFormData<TSalaryPackageResponse>(
        url,
        {
          headers: { Authorization: `Bearer ${peoplesessionToken}` },
          method,
          body: apiFormData,
        },
      );

      return response;
    } catch (error) {
      throw error;
    }
  }

  // Delete a user role from an employee
  async deleteUserRole(employeeId: string, userRoleId: string) {
    try {
      const peoplesessionToken = await getPeopleSessionToken();

      await this.request(
        `/api/employees/${employeeId}/user_roles/${userRoleId}`,
        {
          headers: { Authorization: `Bearer ${peoplesessionToken}` },
          method: "DELETE",
        },
      );

      return {
        success: true,
        data: {
          id: userRoleId,
          employeeId,
          deletedAt: new Date().toISOString(),
        },
      };
    } catch (error) {
      throw error;
    }
  }
}

// Create a singleton instance
export const peopleService = new PeopleService();

export const getPeopleSessionToken = async (): Promise<string | null> => {
  const cookieStore = await cookies();
  return cookieStore.get("people_session_token")?.value || null;
};
