import { cookies } from "next/headers";
import { redirect } from "next/navigation";

export const handleServerTokenExpiration = async (locale: string = "en") => {
  console.log("Server: Token expired, clearing cookies");

  try {
    const cookieStore = await cookies();

    // Clear all auth-related cookies
    cookieStore.delete("main_token");
    cookieStore.delete("core_session_token");
    cookieStore.delete("currSystem");

    sessionStorage.removeItem("user_permissions");

    // Clear any system-specific tokens
    const systems = ["people", "procure", "cm"];
    systems.forEach((system) => {
      cookieStore.delete(`${system}_session_token`);
    });

    redirect(`/${locale}/auth/login`);

    return {
      redirect: true,
      redirectUrl: `/${locale}/auth/login`,
      error: "Token expired. Please log in again.",
    };
  } catch (error) {
    console.error("Error handling token expiration:", error);
    return {
      redirect: true,
      redirectUrl: `/auth/login`,
      error: "Authentication error. Please log in again.",
    };
  }
};

export const isTokenExpirationError = (
  status: number,
  errorMessage?: string,
): boolean => {
  // Check status code first (401 Unauthorized)
  if (status === 401) {
    // If we have an error message, check for specific messages
    if (errorMessage) {
      return (
        errorMessage.includes("Expired access token") ||
        errorMessage.includes("jwt expired") ||
        errorMessage.includes("invalid token") ||
        errorMessage.includes("Token expired")
      );
    }
    // If no specific message but status is 401, assume it's a token issue
    return true;
  }

  return false;
};

/**
 * Gets the user's locale from cookies or returns default
 */
export const getLocaleFromCookies = async (): Promise<string> => {
  const cookieStore = await cookies();
  return cookieStore.get("NEXT_LOCALE")?.value || "en";
};
