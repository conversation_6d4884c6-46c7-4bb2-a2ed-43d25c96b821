import {
  markAllNotificationsAsRead,
  markNotificationAsRead,
} from "@/services/api/notifications";
import { Notification } from "@/types";
import useSWR from "swr";

export function useNotifications(system: string) {
  const { data, error, mutate } = useSWR<Notification[]>(
    `/notifications/${system}`
  );

  const markAsRead = async (id: number) => {
    await markNotificationAsRead(system, id);
    mutate();
  };

  const markAllAsRead = async () => {
    await markAllNotificationsAsRead(system);
    mutate();
  };

  return {
    notifications: data,
    isLoading: !error && !data,
    isError: error,
    markAsRead,
    markAllAsRead,
  };
}
