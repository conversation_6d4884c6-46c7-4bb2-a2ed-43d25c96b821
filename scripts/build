#!/bin/bash -e

SCRIPT_DIR=$(dirname "$(realpath "$0")")

if [[ "$1" == "dev" ]]; then
  COMPOSE_FILE="$SCRIPT_DIR/../development.compose.yml"
  SERVICE_NAME="app"
  cd "$SCRIPT_DIR/.." && docker compose -f "$COMPOSE_FILE" build "$SERVICE_NAME"
elif [[ "$1" == "prod" ]]; then
  # Set default registry host if not provided
  REGISTRY_HOST=${REGISTRY_HOST:-"localhost"}
  IMAGE_NAME="$REGISTRY_HOST/athar/frontend"
  CONTEXT_DIR="$SCRIPT_DIR/../"
  cd "$SCRIPT_DIR/../infra" && docker build \
  -f production.dockerfile \
  -t "$IMAGE_NAME" \
  "$CONTEXT_DIR"
else
  echo "Usage: $0 {dev|prod}"
  exit 1
fi