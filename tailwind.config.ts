import type { Config } from "tailwindcss";

export default {
  darkMode: ["class", ".dark"],
  content: [
    "./src/pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/components/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/app/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
    fontFamily: {
      alex: ["var(--font-alexandria)", "sans-serif"],
      readex_pro: ["var(--font-readex-pro)", "sans-serif"],
    },
    extend: {
      colors: {
        background: {
          DEFAULT: "hsl(var(--background))",
          v2: "hsl(var(--background-v2))",
        },
        foreground: "hsl(var(--foreground))",
        card: {
          DEFAULT: "hsl(var(--card))",
          foreground: "hsl(var(--card-foreground))",
        },
        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))",
        },
        primary: {
          DEFAULT: "hsl(var(--primary))",
          foreground: "hsl(var(--primary-foreground))",
        },
        secondary: {
          DEFAULT: "hsl(var(--secondary))",
          foreground: "hsl(var(--secondary-foreground))",
          "2": "hsl(var(--secondary-2)",
          dark: "hsl(var(--secondary-dark))" /*#242E2C*/,
        },
        muted: {
          DEFAULT: "hsl(var(--muted))",
          foreground: "hsl(var(--muted-foreground))",
        },
        accent: {
          DEFAULT: "hsl(var(--accent))",
          foreground: "hsl(var(--accent-foreground))",
        },
        destructive: {
          DEFAULT: "hsl(var(--destructive))",
          foreground: "hsl(var(--destructive-foreground))",
        },
        border: {
          DEFAULT: "hsl(var(--border))",
        },
        disabled: {
          text: "hsl(var(--disabled-text))",
        },
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        chart: {
          "1": "hsl(var(--chart-1))",
          "2": "hsl(var(--chart-2))",
          "3": "hsl(var(--chart-3))",
          "4": "hsl(var(--chart-4))",
          "5": "hsl(var(--chart-5))",
        },
        success: {
          DEFAULT: "hsl(var(--success))",
          50: "hsla(144, 83%, 96%, 1)",
        },
        hang: {
          DEFAULT: "hsl(var(--hang))",
          50: "hsla(40, 75%, 94%, 1)",
        },
        wait: {
          DEFAULT: "hsl(var(--wait))",
          50: "hsla(201, 100%, 93%, 1)",
        },
        error: { DEFAULT: "hsl(var(--error))", 600: "hsl(var(--error-600))" },
        auth: {
          primary: "hsl(var(--auth-primary))",
          secondary: "hsl(var(--auth-secondary))",
        },
        icons: {
          main: "hsl(var(--main-icon))",
        },
        neu: {
          100: "rgba(243, 244, 246, 1)",
          500: "rgba(107, 114, 128, 1)",
        },
      },
      backgroundImage: {
        "dashed-border": `url("data:image/svg+xml,%3csvg width='100%25' height='100%25' xmlns='http://www.w3.org/2000/svg'%3e%3cline x1='0' y1='1' x2='100%25' y2='1' fill='none' stroke='black' stroke-width='1' stroke-dasharray='5%2c10' stroke-dashoffset='15' stroke-linecap='square'/%3e%3cline x1='0' y1='100%25' x2='100%25' y2='99%25' fill='none' stroke='black' stroke-width='1' stroke-dasharray='5%2c10' stroke-dashoffset='15' stroke-linecap='square'/%3e%3c/svg%3e")`,
      },
      borderRadius: {
        lg: "var(--radius)",
        md: "calc(var(--radius) - 2px)",
        sm: "calc(var(--radius) - 4px)",
      },
      keyframes: {
        shimmer: {
          "0%": { transform: "translateX(-100%)" },
          "100%": { transform: "translateX(100%)" },
        },
      },
      animation: {
        shimmer: "shimmer 2s infinite",
      },
    },
  },
  plugins: [require("tailwindcss-animate")],
} satisfies Config;
