import createNextIntlPlugin from "next-intl/plugin";
import {NextConfig} from "next";

const withNextIntl = createNextIntlPlugin();

/** @type {import('next').NextConfig} */
const nextConfig: NextConfig = {
    output: "standalone",
    // reactStrictMode: false,
    experimental: {
        serverActions: {
            bodySizeLimit: "5mb",
        },
    },
    // serverExternalPackages: [],
    images: {
        remotePatterns: [
            {
                protocol: "https",
                hostname: "minio-api.ems.atharyouth.org",
            },
        ],
    },
    webpack(config) {
        config.module.rules.push({
            test: /\.svg$/,
            use: ["@svgr/webpack"],
        });

        return config;
    },
    eslint: {
        ignoreDuringBuilds: true,
    },
};

export default withNextIntl(nextConfig);
